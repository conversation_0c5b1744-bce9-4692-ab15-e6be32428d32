import { NextRequest, NextResponse } from 'next/server';
import { 
  getQueryCacheStats, 
  resetQueryCache, 
  searchCachedQueries, 
  getPopularQueries,
  cleanupExpiredQueries 
} from '@/lib/query-cache';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const search = searchParams.get('search');
    const limit = parseInt(searchParams.get('limit') || '10');

    switch (action) {
      case 'stats':
        const stats = await getQueryCacheStats();
        return NextResponse.json({
          success: true,
          data: stats
        });

      case 'search':
        if (!search) {
          return NextResponse.json(
            { success: false, error: 'مصطلح البحث مطلوب' },
            { status: 400 }
          );
        }
        const searchResults = await searchCachedQueries(search);
        return NextResponse.json({
          success: true,
          data: searchResults
        });

      case 'popular':
        const popularQueries = await getPopularQueries(limit);
        return NextResponse.json({
          success: true,
          data: popularQueries
        });

      default:
        return NextResponse.json(
          { success: false, error: 'إجراء غير صحيح' },
          { status: 400 }
        );
    }
  } catch (error: any) {
    console.error('خطأ في API التخزين المؤقت:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'فشل في الوصول إلى ذاكرة التخزين المؤقت',
        details: error.message
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { action } = await request.json();

    switch (action) {
      case 'cleanup':
        await cleanupExpiredQueries();
        return NextResponse.json({
          success: true,
          message: 'تم تنظيف ذاكرة التخزين المؤقت بنجاح'
        });

      case 'reset':
        await resetQueryCache();
        return NextResponse.json({
          success: true,
          message: 'تم إعادة تعيين ذاكرة التخزين المؤقت بنجاح'
        });

      default:
        return NextResponse.json(
          { success: false, error: 'إجراء غير صحيح' },
          { status: 400 }
        );
    }
  } catch (error: any) {
    console.error('خطأ في إدارة التخزين المؤقت:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'فشل في إدارة ذاكرة التخزين المؤقت',
        details: error.message
      },
      { status: 500 }
    );
  }
}
