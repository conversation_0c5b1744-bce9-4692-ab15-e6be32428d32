'use client';

import { useState } from 'react';
import { Database, Server, Lock, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';
import ProgressBar from './ProgressBar';

interface DatabaseSetupProps {
  onConnectionSuccess: (data: any) => void;
}

interface ConnectionData {
  server: string;
  database: string;
  username: string;
  password: string;
  port: string;
  trustServerCertificate: boolean;
}

export default function DatabaseSetup({ onConnectionSuccess }: DatabaseSetupProps) {
  const [connectionData, setConnectionData] = useState<ConnectionData>({
    server: process.env.NEXT_PUBLIC_DEFAULT_DB_SERVER || 'localhost',
    database: process.env.NEXT_PUBLIC_DEFAULT_DB_DATABASE || 'SalesTempDB',
    username: process.env.NEXT_PUBLIC_DEFAULT_DB_USERNAME || 'myuser',
    password: process.env.NEXT_PUBLIC_DEFAULT_DB_PASSWORD || 'Aa227520',
    port: process.env.NEXT_PUBLIC_DEFAULT_DB_PORT || '1433',
    trustServerCertificate: process.env.NEXT_PUBLIC_DEFAULT_DB_TRUST_CERT === 'true' || true
  });

  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'idle' | 'testing' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState('');
  const [isIndexing, setIsIndexing] = useState(false);
  const [progressSteps, setProgressSteps] = useState([
    { id: 'connection', title: 'الاتصال بقاعدة البيانات', description: 'جاري الاتصال...', status: 'pending' as const },
    { id: 'extraction', title: 'استخراج البيانات', description: 'جاري استخراج الجداول والأعمدة...', status: 'pending' as const },
    { id: 'enhancement', title: 'تحسين الأوصاف', description: 'جاري تحسين أوصاف الجداول باستخدام الذكاء الصناعي...', status: 'pending' as const },
    { id: 'intents', title: 'إنشاء ملف النيات', description: 'جاري إنشاء ملف النيات للاستعلامات...', status: 'pending' as const },
    { id: 'entities', title: 'إنشاء ملف الكيانات', description: 'جاري إنشاء ملف الكيانات...', status: 'pending' as const },
    { id: 'synonyms', title: 'إنشاء ملف المرادفات', description: 'جاري إنشاء ملف المرادفات...', status: 'pending' as const },
    { id: 'usecases', title: 'إنشاء حالات الاستخدام', description: 'جاري إنشاء ملف حالات الاستخدام...', status: 'pending' as const },
    { id: 'classification', title: 'التصنيف والفئات', description: 'جاري إنشاء التصنيف والفئات...', status: 'pending' as const },
    { id: 'saving', title: 'حفظ البيانات', description: 'جاري حفظ البيانات...', status: 'pending' as const }
  ]);
  const [currentStep, setCurrentStep] = useState(0);

  const handleInputChange = (field: keyof ConnectionData, value: string | boolean) => {
    setConnectionData(prev => ({
      ...prev,
      [field]: value
    }));
    setConnectionStatus('idle');
    setErrorMessage('');
  };

  const testConnection = async () => {
    setIsConnecting(true);
    setConnectionStatus('testing');
    setErrorMessage('');

    try {
      const response = await fetch('/api/database/test-connection', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(connectionData),
      });

      const result = await response.json();

      if (response.ok) {
        setConnectionStatus('success');
      } else {
        setConnectionStatus('error');
        setErrorMessage(result.error || 'فشل في الاتصال بقاعدة البيانات');
      }
    } catch (error) {
      setConnectionStatus('error');
      setErrorMessage('خطأ في الشبكة أو الخادم');
    } finally {
      setIsConnecting(false);
    }
  };

  const updateProgressStep = (stepId: string, status: 'pending' | 'in-progress' | 'completed' | 'error') => {
    setProgressSteps(prev => prev.map(step =>
      step.id === stepId ? { ...step, status } : step
    ));
  };

  const simulateProgress = () => {
    const steps = ['connection', 'extraction', 'enhancement', 'intents', 'entities', 'synonyms', 'usecases', 'classification', 'saving'];
    let currentIndex = 0;

    const interval = setInterval(() => {
      if (currentIndex < steps.length) {
        // Mark current step as in-progress
        updateProgressStep(steps[currentIndex], 'in-progress');
        setCurrentStep(currentIndex + 1);

        // After some time, mark as completed and move to next
        setTimeout(() => {
          updateProgressStep(steps[currentIndex], 'completed');
          currentIndex++;

          if (currentIndex >= steps.length) {
            clearInterval(interval);
          }
        }, 2000 + Math.random() * 3000); // Random delay between 2-5 seconds
      }
    }, 500);

    return interval;
  };

  const startIndexing = async () => {
    setIsIndexing(true);
    setCurrentStep(0);

    // Reset all steps to pending
    setProgressSteps(prev => prev.map(step => ({ ...step, status: 'pending' as const })));

    // Start progress simulation
    const progressInterval = simulateProgress();

    try {
      const response = await fetch('/api/database/index-schema', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(connectionData),
      });

      const result = await response.json();

      // Clear the simulation
      clearInterval(progressInterval);

      if (response.ok) {
        // Mark all steps as completed
        setProgressSteps(prev => prev.map(step => ({ ...step, status: 'completed' as const })));
        setCurrentStep(progressSteps.length);

        // Wait a moment to show completion, then proceed
        setTimeout(() => {
          onConnectionSuccess({
            ...connectionData,
            schema: result.schema,
            indexed: true
          });
        }, 1000);
      } else {
        // Mark current step as error
        setProgressSteps(prev => prev.map((step, index) =>
          index === currentStep - 1 ? { ...step, status: 'error' as const } : step
        ));
        setErrorMessage(result.error || 'فشل في فهرسة قاعدة البيانات');
      }
    } catch (error) {
      clearInterval(progressInterval);
      setProgressSteps(prev => prev.map((step, index) =>
        index === currentStep - 1 ? { ...step, status: 'error' as const } : step
      ));
      setErrorMessage('خطأ في فهرسة قاعدة البيانات');
    } finally {
      setIsIndexing(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl p-8 w-full max-w-md">
        <div className="text-center mb-8">
          <div className="mx-auto w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mb-4">
            <Database className="w-8 h-8 text-blue-600 dark:text-blue-400" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            وكيل الذكاء الاصطناعي لـ SQL
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            اتصل بقاعدة بيانات SQL Server للبدء
          </p>
        </div>

        <form className="space-y-4" onSubmit={(e) => e.preventDefault()}>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <Server className="w-4 h-4 inline mr-2" />
              اسم الخادم
            </label>
            <input
              type="text"
              value={connectionData.server}
              onChange={(e) => handleInputChange('server', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              placeholder="localhost أو عنوان IP"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <Database className="w-4 h-4 inline mr-2" />
              اسم قاعدة البيانات
            </label>
            <input
              type="text"
              value={connectionData.database}
              onChange={(e) => handleInputChange('database', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              placeholder="اسم قاعدة البيانات"
              required
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                اسم المستخدم
              </label>
              <input
                type="text"
                value={connectionData.username}
                onChange={(e) => handleInputChange('username', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                placeholder="sa"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                المنفذ
              </label>
              <input
                type="text"
                value={connectionData.port}
                onChange={(e) => handleInputChange('port', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                placeholder="1433"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <Lock className="w-4 h-4 inline mr-2" />
              كلمة المرور
            </label>
            <input
              type="password"
              value={connectionData.password}
              onChange={(e) => handleInputChange('password', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              placeholder="كلمة المرور"
            />
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="trustCert"
              checked={connectionData.trustServerCertificate}
              onChange={(e) => handleInputChange('trustServerCertificate', e.target.checked)}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
            />
            <label htmlFor="trustCert" className="mr-2 text-sm text-gray-700 dark:text-gray-300">
              الثقة في شهادة الخادم
            </label>
          </div>

          {errorMessage && (
            <div className="flex items-center p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
              <span className="text-red-700 dark:text-red-400 text-sm">{errorMessage}</span>
            </div>
          )}

          {connectionStatus === 'success' && (
            <div className="flex items-center p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
              <CheckCircle className="w-5 h-5 text-green-500 mr-2" />
              <span className="text-green-700 dark:text-green-400 text-sm">تم الاتصال بنجاح!</span>
            </div>
          )}

          <div className="space-y-3">
            <button
              type="button"
              onClick={testConnection}
              disabled={isConnecting || !connectionData.server || !connectionData.database}
              className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center"
            >
              {isConnecting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  جاري الاختبار...
                </>
              ) : (
                'اختبار الاتصال'
              )}
            </button>

            {connectionStatus === 'success' && (
              <button
                type="button"
                onClick={startIndexing}
                disabled={isIndexing}
                className="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center"
              >
                {isIndexing ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    جاري الفهرسة...
                  </>
                ) : (
                  'البدء والفهرسة'
                )}
              </button>
            )}
          </div>
        </form>
      </div>

      {/* شريط التقدم */}
      <ProgressBar
        steps={progressSteps}
        currentStep={currentStep}
        isVisible={isIndexing}
      />
    </div>
  );
}
