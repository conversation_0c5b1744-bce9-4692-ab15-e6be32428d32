{"metadata": {"version": "1.0", "last_updated": "2024-12-19", "description": "مخطط قاعدة بيانات نظام إدارة المبيعات والمخزون"}, "tables": {"tbltemp_ItemsMain": {"name_arabic": "العناصر الرئيسية", "description": "يحتوي هذا الجدول على تفاصيل المعاملات الكاملة المتعلقة بالمنتجات، بما في ذلك معلومات المبيعات، المشتريات، العملاء، الفروع، والمخازن. يُستخدم لتتبع حركة المنتجات والمعاملات المالية المرتبطة بها.", "business_purpose": "تتبع حركة المنتجات وتحليل المبيعات والمشتريات وبيانات العملاء والفروع", "primary_key": ["ID"], "columns": {"ID": {"name_arabic": "معر<PERSON> السجل", "type": "bigint", "description": "رقم تعريفي فريد لكل سجل في الجدول، يضمن عدم تكرار السجلات", "nullable": false, "is_primary_key": true, "sample_values": [1001, 1002, 1003]}, "ParentID": {"name_arabic": "معرف العنصر الأصلي", "type": "bigint", "description": "الرقم التعريفي للسجل الأصلي في حالة وجود علاقة تسلسلية أو تفريع بين السجلات", "nullable": true, "is_primary_key": false, "sample_values": [500, 600, null]}, "DocumentID": {"name_arabic": "معرف الوثيقة", "type": "bigint", "description": "الرقم التعريفي للوثيقة المرتبطة بعملية المعاملة (فاتورة، سند، إلخ)", "nullable": false, "is_primary_key": false, "sample_values": [2001, 2002, 2003]}, "RecordNumber": {"name_arabic": "رقم السجل", "type": "bigint", "description": "رقم تسلسلي للسجل داخل الوثيقة المرتبطة", "nullable": false, "is_primary_key": false, "sample_values": [1, 2, 3]}, "RecordID": {"name_arabic": "معرف السجل المرتبط", "type": "bigint", "description": "الرقم التعريفي للسجل في الوثيقة المرتبطة", "nullable": false, "is_primary_key": false, "sample_values": [3001, 3002, 3003]}, "TheDate": {"name_arabic": "تاريخ المعاملة", "type": "datetime", "description": "التاريخ والوقت الدقيق لإدخال أو تنفيذ عملية المعاملة في النظام", "nullable": false, "is_primary_key": false, "sample_values": ["2024-01-15 10:30:00", "2024-01-16 14:45:00"]}, "ClientID": {"name_arabic": "معر<PERSON> العميل", "type": "bigint", "description": "الرقم التعريفي للعميل المرتبط بعملية المعاملة", "nullable": true, "is_primary_key": false, "sample_values": [101, 102, 103]}, "ClientName": {"name_arabic": "اسم العميل", "type": "<PERSON><PERSON><PERSON>(150)", "description": "الاسم الكامل أو التجاري للعميل المرتبط بعملية المعاملة", "nullable": true, "is_primary_key": false, "sample_values": ["<PERSON><PERSON><PERSON><PERSON> أحمد", "شركة XYZ", "علي عبدالله"]}, "CurrencyID": {"name_arabic": "معرف العملة", "type": "bigint", "description": "الرقم التعريفي للعملة المستخدمة في قيمة المعاملة", "nullable": true, "is_primary_key": false, "sample_values": [1, 2, 3]}, "CurrencyName": {"name_arabic": "اسم العملة", "type": "<PERSON><PERSON><PERSON>(150)", "description": "اسم العملة المستخدمة في قيمة المعاملة (مثال: ريال، دولار، إلخ)", "nullable": false, "is_primary_key": false, "sample_values": ["ريال يمني", "دولار أمريكي", "ريال سعودي"]}, "TheMethodID": {"name_arabic": "معرف طريقة الدفع", "type": "bigint", "description": "الرقم التعريفي لطريقة الدفع المستخدمة في المعاملة", "nullable": true, "is_primary_key": false, "sample_values": [1, 2, 3]}, "TheMethod": {"name_arabic": "طريقة الدفع", "type": "<PERSON><PERSON><PERSON>(150)", "description": "وصف طريقة الدفع المستخدمة في المعاملة (مثال: نقداً، آجل، بطاقة)", "nullable": true, "is_primary_key": false, "sample_values": ["نقداً", "آجل", "بطاقة ائتمان"]}, "UserID": {"name_arabic": "معرف المستخدم", "type": "bigint", "description": "الرقم التعريفي للمستخدم الذي قام بإدخال أو تنفيذ عملية المعاملة", "nullable": false, "is_primary_key": false, "sample_values": [1001, 1002, 1003]}, "BranchID": {"name_arabic": "معرف الفرع", "type": "bigint", "description": "الرقم التعريفي للفرع الذي تم فيه تنفيذ عملية المعاملة", "nullable": false, "is_primary_key": false, "sample_values": [1, 2, 3]}, "BranchName": {"name_arabic": "اسم الفرع", "type": "<PERSON><PERSON><PERSON>(150)", "description": "الاسم التجاري أو الوصفي للفرع الذي تم فيه تنفيذ عملية المعاملة", "nullable": false, "is_primary_key": false, "sample_values": ["فرع صنعاء", "فرع عدن", "فرع إب"]}, "DocumentName": {"name_arabic": "اسم الوثيقة", "type": "<PERSON><PERSON><PERSON>(150)", "description": "نوع الوثيقة المرتبطة بالمعاملة (مثال: فاتورة مبيعات، فاتورة مشتريات، سند صرف)", "nullable": false, "is_primary_key": false, "sample_values": ["فاتورة مبيعات", "فاتورة مشتريات", "سند صرف"]}, "ItemID": {"name_arabic": "معر<PERSON> المنتج", "type": "bigint", "description": "الرقم التعريفي للمنتج (الصن<PERSON>) المشارك في عملية المعاملة", "nullable": true, "is_primary_key": false, "sample_values": [201, 202, 203]}, "ItemName": {"name_arabic": "اسم المنتج", "type": "<PERSON><PERSON><PERSON>(200)", "description": "الاسم التجاري أو الوصفي للمنتج (الصنف) المشارك في عملية المعاملة", "nullable": true, "is_primary_key": false, "sample_values": ["كولا", "فانتا", "سبريت"]}, "CategoryID": {"name_arabic": "معرف الفئة", "type": "bigint", "description": "الرقم التعريفي لفئة المنتج (التصنيف) المشارك في عملية المعاملة", "nullable": true, "is_primary_key": false, "sample_values": [10, 20, 30]}, "CategoryName": {"name_arabic": "اسم الفئة", "type": "<PERSON><PERSON><PERSON>(200)", "description": "اسم فئة المنتج (التصنيف) المشارك في عملية المعاملة", "nullable": true, "is_primary_key": false, "sample_values": ["مشروبات غازية", "مواد غذائية", "منظفات"]}, "ItemTypeID": {"name_arabic": "معرف نوع المنتج", "type": "bigint", "description": "الرقم التعريفي لنوع المنتج (مادي، خدمي، رقمي)", "nullable": false, "is_primary_key": false, "sample_values": [1, 2, 3]}, "ItemType": {"name_arabic": "نوع المنتج", "type": "<PERSON><PERSON><PERSON>(150)", "description": "وصف نوع المنتج (مادي، خدمي، رقمي)", "nullable": true, "is_primary_key": false, "sample_values": ["من<PERSON><PERSON> مادي", "خدمة", "منتج رقمي"]}, "ISActive": {"name_arabic": "حالة النشاط", "type": "bit", "description": "حالة المنتج أو العملية (1 = نشط، 0 = غير نشط)", "nullable": true, "is_primary_key": false, "sample_values": [1, 0]}, "UnitID": {"name_arabic": "معرف الوحدة", "type": "bigint", "description": "الرقم التعريفي للوحدة المستخدمة لقياس الكمية في المعاملة", "nullable": true, "is_primary_key": false, "sample_values": [1, 2, 3]}, "UnitName": {"name_arabic": "اسم الوحدة", "type": "<PERSON><PERSON><PERSON>(150)", "description": "اسم الوحدة المستخدمة لقياس الكمية في المعاملة (مثال: قطعة، علبة، كيلو)", "nullable": true, "is_primary_key": false, "sample_values": ["قطعة", "علبة", "كيلوغرام"]}, "ExchangeFactor": {"name_arabic": "عامل التحويل", "type": "numeric(18,6)", "description": "معامل التحويل بين الوحدة المستخدمة والوحدة الأساسية للمنتج", "nullable": false, "is_primary_key": false, "sample_values": [1.0, 12.0, 0.5]}, "UnitPrice": {"name_arabic": "سعر الوحدة", "type": "numeric(18,6)", "description": "سعر الوحدة الواحدة من المنتج بالعملة المحلية للمعاملة", "nullable": true, "is_primary_key": false, "sample_values": [100.0, 150.5, 200.75]}, "Quantity": {"name_arabic": "الكمية", "type": "numeric(18,6)", "description": "الكمية الفعلية للمنتج المتعامل بها في عملية المعاملة", "nullable": true, "is_primary_key": false, "sample_values": [10.0, 5.5, 20.25]}, "Amount": {"name_arabic": "المبلغ الإجمالي", "type": "numeric(18,6)", "description": "المبلغ الإجمالي لقيمة المعاملة (سعر الوحدة × الكمية) بالعملة المحلية", "nullable": true, "is_primary_key": false, "sample_values": [1000.0, 827.75, 4015.0]}, "MCAmount": {"name_arabic": "المبلغ بالعملة الرئيسية", "type": "numeric(18,6)", "description": "المبلغ الإجمالي لقيمة المعاملة بعد التحويل إلى العملة الرئيسية للشركة", "nullable": true, "is_primary_key": false, "sample_values": [1000.0, 827.75, 4015.0]}, "StoreID": {"name_arabic": "معرف الم<PERSON><PERSON>ن", "type": "bigint", "description": "الرقم التعريفي للمخزن الذي تم فيه تنفيذ عملية المعاملة", "nullable": true, "is_primary_key": false, "sample_values": [10, 20, 30]}, "StoreName": {"name_arabic": "اسم المخزن", "type": "<PERSON><PERSON><PERSON>(150)", "description": "اسم المخزن الذي تم فيه تنفيذ عملية المعاملة", "nullable": true, "is_primary_key": false, "sample_values": ["المخزن الرئيسي", "مخزن الفرع", "المستودع"]}, "ExchangePrice": {"name_arabic": "سعر الصرف", "type": "numeric(18,6)", "description": "سعر الصرف بين العملة المحلية وعملة المخزون", "nullable": false, "is_primary_key": false, "sample_values": [1.0, 0.5, 2.0]}, "ExchangePriceCurrencyInvetory": {"name_arabic": "سعر الصرف للمخزون", "type": "decimal(18,6)", "description": "سعر الصرف بين العملة المحلية وعملة المخزون المستخدمة في حسابات المخزون", "nullable": false, "is_primary_key": false, "sample_values": [1.0, 0.5, 2.0]}, "UserName": {"name_arabic": "اسم المستخدم", "type": "<PERSON><PERSON><PERSON>(150)", "description": "الاسم الكامل للمستخدم الذي قام بإدخال أو تنفيذ عملية المعاملة", "nullable": false, "is_primary_key": false, "sample_values": ["<PERSON><PERSON><PERSON><PERSON> محمد", "سارة علي", "عب<PERSON><PERSON><PERSON><PERSON><PERSON> أحمد"]}, "TheYear": {"name_arabic": "السنة", "type": "int", "description": "السنة الميلادية التي تم فيها تنفيذ عملية المعاملة", "nullable": true, "is_primary_key": false, "sample_values": [2024, 2023, 2022]}, "Notes": {"name_arabic": "ملاحظات", "type": "<PERSON><PERSON><PERSON>(255)", "description": "ملاحظات إضافية توضيحية حول عملية المعاملة", "nullable": true, "is_primary_key": false, "sample_values": ["عميل وليد", "عر<PERSON> خاص", "<PERSON><PERSON><PERSON> عاجل"]}, "SerialNumber": {"name_arabic": "الرقم التسلسلي", "type": "<PERSON><PERSON><PERSON>(150)", "description": "الرقم التسلسلي الفريد للمنتج (في حالة المنتجات ذات التسلسل الفريد)", "nullable": true, "is_primary_key": false, "sample_values": ["SN001234", "SN005678", "SN009012"]}, "Barcode": {"name_arabic": "الباركود", "type": "<PERSON><PERSON><PERSON>(150)", "description": "ر<PERSON>ز الباركود الخاص بالمنتج لتسهيل عملية البحث والتتبع", "nullable": true, "is_primary_key": false, "sample_values": ["1234567890123", "9876543210987", "1111111111111"]}, "ItemNumber": {"name_arabic": "رقم المنتج", "type": "bigint", "description": "الرقم التعريفي الداخلي للمنتج في النظام", "nullable": false, "is_primary_key": false, "sample_values": [1001, 1002, 1003]}}}, "tbltemp_Inv_MainInvoice": {"name_arabic": "الفواتير الرئيسية", "description": "يحتوي هذا الجدول على تفاصيل الفواتير الأساسية، بما في ذلك معلومات المنتجات المباعة، الأسعار، الكميات، العملاء، والبيانات المالية. يُستخدم بشكل خاص لتتبع المعاملات المالية المفصلة.", "business_purpose": "تتبع تفاصيل الفواتير وتحليل البيانات المالية والمنتجات المباعة", "primary_key": ["ID"], "columns": {"ID": {"name_arabic": "معر<PERSON> السجل", "type": "bigint", "description": "رقم تعريفي فريد لكل سجل في الجدول، يضمن عدم تكرار السجلات", "nullable": false, "is_primary_key": true, "sample_values": [1001, 1002, 1003]}, "DocumentName": {"name_arabic": "اسم الوثيقة", "type": "<PERSON><PERSON><PERSON>(150)", "description": "نوع الوثيقة المرتبطة بالفاتورة (عادة 'فاتورة مبيعات' أو 'فاتورة مشتريات')", "nullable": false, "is_primary_key": false, "sample_values": ["فاتورة مبيعات", "فاتورة مشتريات"]}, "RecordID": {"name_arabic": "معرف السجل المرتبط", "type": "bigint", "description": "الرقم التعريفي للسجل في الوثيقة المرتبطة", "nullable": false, "is_primary_key": false, "sample_values": [3001, 3002, 3003]}, "InvoiceID": {"name_arabic": "معرف الفاتورة", "type": "bigint", "description": "الرقم التعريفي للفاتورة الرئيسية", "nullable": false, "is_primary_key": false, "sample_values": [5001, 5002, 5003]}, "DetailsID": {"name_arabic": "معرف التفاصيل", "type": "bigint", "description": "الرقم التعريفي لسجل التفاصيل داخل الفاتورة", "nullable": false, "is_primary_key": false, "sample_values": [6001, 6002, 6003]}, "TheDate": {"name_arabic": "تاريخ الفاتورة", "type": "datetime", "description": "تاريخ إصدار الفاتورة", "nullable": true, "is_primary_key": false, "sample_values": ["2024-01-15 10:30:00", "2024-01-16 14:45:00"]}, "EnterTime": {"name_arabic": "وقت الإدخال", "type": "datetime", "description": "الوقت الدقيق لإدخال الفاتورة في النظام", "nullable": true, "is_primary_key": false, "sample_values": ["2024-01-15 10:30:00", "2024-01-16 14:45:00"]}, "ItemID": {"name_arabic": "معر<PERSON> المنتج", "type": "bigint", "description": "الرقم التعريفي للمنتج (الصنف) المباع في الفاتورة", "nullable": true, "is_primary_key": false, "sample_values": [201, 202, 203]}, "UnitID": {"name_arabic": "معرف الوحدة", "type": "bigint", "description": "الرقم التعريفي للوحدة المستخدمة لقياس الكمية في الفاتورة", "nullable": true, "is_primary_key": false, "sample_values": [1, 2, 3]}, "UnitPrice": {"name_arabic": "سعر الوحدة", "type": "numeric(18,6)", "description": "سعر الوحدة الواحدة من المنتج في الفاتورة بالعملة المحلية", "nullable": true, "is_primary_key": false, "sample_values": [100.0, 150.5, 200.75]}, "Quantity": {"name_arabic": "الكمية", "type": "numeric(18,6)", "description": "الكمية المباعة من المنتج في الفاتورة", "nullable": true, "is_primary_key": false, "sample_values": [10.0, 5.5, 20.25]}, "TotalAmount": {"name_arabic": "المبلغ الإجمالي للفاتورة", "type": "numeric(18,6)", "description": "المبلغ الإجمالي لقيمة الفاتورة بالعملة المحلية", "nullable": true, "is_primary_key": false, "sample_values": [1000.0, 827.75, 4015.0]}, "MainUnitQuantity": {"name_arabic": "الكمية بالوحدة الرئيسية", "type": "numeric(37,12)", "description": "الكمية المباعة مُحولة إلى الوحدة الرئيسية للمنتج", "nullable": true, "is_primary_key": false, "sample_values": [10.0, 5.5, 20.25]}, "MainUnitPrice": {"name_arabic": "سعر الوحدة الرئيسية", "type": "numeric(38,20)", "description": "سعر الوحدة الرئيسية للمنتج بدقة عالية", "nullable": true, "is_primary_key": false, "sample_values": [100.0, 150.5]}, "StoreID": {"name_arabic": "معرف الم<PERSON><PERSON>ن", "type": "bigint", "description": "الرقم التعريفي للمخزن المرتبط بعملية البيع في الفاتورة", "nullable": true, "is_primary_key": false, "sample_values": [10, 20, 30]}, "BranchID": {"name_arabic": "معرف الفرع", "type": "bigint", "description": "الرقم التعريفي للفرع المرتبط بعملية البيع في الفاتورة", "nullable": true, "is_primary_key": false, "sample_values": [1, 2, 3]}, "ExchangeFactor": {"name_arabic": "عامل التحويل", "type": "numeric(18,6)", "description": "عامل التحويل بين الوحدة المستخدمة والوحدة الأساسية للمنتج", "nullable": false, "is_primary_key": false, "sample_values": [1.0, 12.0, 0.5]}, "ClientID": {"name_arabic": "معر<PERSON> العميل", "type": "bigint", "description": "الرقم التعريفي للعميل الذي تم إصدار الفاتورة له", "nullable": true, "is_primary_key": false, "sample_values": [101, 102, 103]}, "MCAmount": {"name_arabic": "المبلغ بالعملة الرئيسية", "type": "decimal(38,13)", "description": "المبلغ الإجمالي للفاتورة بعد التحويل إلى العملة الرئيسية للشركة", "nullable": true, "is_primary_key": false, "sample_values": [1000.0, 827.75, 4015.0]}, "NewSubItemEntryID": {"name_arabic": "معرف العنصر الفرعي الجديد", "type": "bigint", "description": "الرقم التعريفي لعنصر فرعي جديد تم إدخاله في النظام", "nullable": false, "is_primary_key": false, "sample_values": [7001, 7002, 7003]}, "CurrencyID": {"name_arabic": "معرف العملة", "type": "bigint", "description": "الرقم التعريفي للعملة المستخدمة في الفاتورة", "nullable": true, "is_primary_key": false, "sample_values": [1, 2, 3]}, "TheMethod": {"name_arabic": "طريقة الدفع", "type": "<PERSON><PERSON><PERSON>(150)", "description": "وصف طريقة الدفع المستخدمة في الفاتورة (مثال: نقداً، آجل، بطاقة)", "nullable": true, "is_primary_key": false, "sample_values": ["نقداً", "آجل", "بطاقة ائتمان"]}, "ExchangePrice": {"name_arabic": "سعر الصرف", "type": "numeric(18,6)", "description": "سعر الصرف بين العملة المحلية وعملة المخزون", "nullable": true, "is_primary_key": false, "sample_values": [1.0, 0.5, 2.0]}, "TotalAmountByCurrencyInvetory": {"name_arabic": "المبلغ الإجمالي بعملة المخزون", "type": "numeric(38,13)", "description": "المبلغ الإجمالي للفاتورة بالعملة المستخدمة في حسابات المخزون", "nullable": true, "is_primary_key": false, "sample_values": [1000.0, 827.75, 4015.0]}}}}, "intelligent_classification": {"المنتجات": {"description": "تشمل جميع المعلومات المتعلقة بالمنتجات والكميات والأسعار والوحدات", "columns": ["tbltemp_ItemsMain.ItemID", "tbltemp_ItemsMain.ItemName", "tbltemp_ItemsMain.CategoryID", "tbltemp_ItemsMain.CategoryName", "tbltemp_ItemsMain.ItemTypeID", "tbltemp_ItemsMain.ItemType", "tbltemp_ItemsMain.UnitID", "tbltemp_ItemsMain.UnitName", "tbltemp_ItemsMain.UnitPrice", "tbltemp_ItemsMain.Quantity", "tbltemp_ItemsMain.Amount", "tbltemp_ItemsMain.MCAmount", "tbltemp_ItemsMain.ExchangeFactor", "tbltemp_ItemsMain.ExchangePrice", "tbltemp_ItemsMain.ExchangePriceCurrencyInvetory", "tbltemp_ItemsMain.SerialNumber", "tbltemp_ItemsMain.Barcode", "tbltemp_ItemsMain.ItemNumber", "tbltemp_Inv_MainInvoice.ItemID", "tbltemp_Inv_MainInvoice.UnitID", "tbltemp_Inv_MainInvoice.UnitPrice", "tbltemp_Inv_MainInvoice.Quantity", "tbltemp_Inv_MainInvoice.MainUnitQuantity", "tbltemp_Inv_MainInvoice.MainUnitPrice", "tbltemp_Inv_MainInvoice.ExchangeFactor", "tbltemp_Inv_MainInvoice.ExchangePrice"], "business_value": "تحليل أداء المنتجات ومتابعة المخزون وتحديد الأكثر مبيعاً"}, "العملاء": {"description": "تشمل معلومات عن هوية العملاء وسجل مشترياتهم", "columns": ["tbltemp_ItemsMain.ClientID", "tbltemp_ItemsMain.ClientName", "tbltemp_Inv_MainInvoice.ClientID"], "business_value": "تحليل سلوك العملاء وتحديد العملاء الأكثر قيمة"}, "المخازن": {"description": "تشمل معلومات عن المواقع والمخازن والفرعية", "columns": ["tbltemp_ItemsMain.StoreID", "tbltemp_ItemsMain.StoreName", "tbltemp_ItemsMain.BranchID", "tbltemp_ItemsMain.BranchName", "tbltemp_Inv_MainInvoice.StoreID", "tbltemp_Inv_MainInvoice.BranchID"], "business_value": "مراقبة حركة المخازن وتحليل أداء الفروع"}, "المبالغ": {"description": "تشمل معلومات عن القيم المالية والأسعار والتحويلات النقدية", "columns": ["tbltemp_ItemsMain.UnitPrice", "tbltemp_ItemsMain.Amount", "tbltemp_ItemsMain.MCAmount", "tbltemp_ItemsMain.ExchangePrice", "tbltemp_ItemsMain.ExchangePriceCurrencyInvetory", "tbltemp_Inv_MainInvoice.UnitPrice", "tbltemp_Inv_MainInvoice.TotalAmount", "tbltemp_Inv_MainInvoice.MCAmount", "tbltemp_Inv_MainInvoice.TotalAmountByCurrencyInvetory"], "business_value": "تحليل الإيرادات والربحية ومتابعة التحويلات النقدية"}, "التواريخ": {"description": "تشمل معلومات عن التواريخ والتوقيتات لجميع العمليات", "columns": ["tbltemp_ItemsMain.TheDate", "tbltemp_Inv_MainInvoice.TheDate", "tbltemp_Inv_MainInvoice.EnterTime", "tbltemp_ItemsMain.TheYear"], "business_value": "تحليل الاتجاهات الزمنية وتحديد مواسم الذروة"}, "الفواتير": {"description": "تشمل معلومات عن الوثائق والفواتير والهويات المرتبطة", "columns": ["tbltemp_ItemsMain.DocumentID", "tbltemp_ItemsMain.RecordNumber", "tbltemp_ItemsMain.RecordID", "tbltemp_ItemsMain.DocumentName", "tbltemp_Inv_MainInvoice.InvoiceID", "tbltemp_Inv_MainInvoice.DetailsID", "tbltemp_Inv_MainInvoice.DocumentName", "tbltemp_Inv_MainInvoice.RecordID"], "business_value": "تتبع الفواتير ومتابعة العمليات الوثائقية"}, "المحاسبة": {"description": "تشمل معلومات عن العملات ومراكز التكلفة والتحويلات المحاسبية", "columns": ["tbltemp_ItemsMain.CurrencyID", "tbltemp_ItemsMain.CurrencyName", "tbltemp_ItemsMain.MCAmount", "tbltemp_ItemsMain.ExchangePrice", "tbltemp_ItemsMain.ExchangePriceCurrencyInvetory", "tbltemp_Inv_MainInvoice.CurrencyID", "tbltemp_Inv_MainInvoice.MCAmount", "tbltemp_Inv_MainInvoice.ExchangePrice", "tbltemp_Inv_MainInvoice.TotalAmountByCurrencyInvetory"], "business_value": "إدارة المحاسبة ومتابعة التحويلات والعملات"}}}