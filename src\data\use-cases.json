{"use_cases": [{"id": "UC001", "name": "تحديد أعلى منتج مبيعاً", "description": "تحديد المنتج الذي حقق أعلى مبيعات في فترة زمنية معينة", "category": "تحليل المبيعات", "tables_involved": ["tbltemp_ItemsMain", "tbltemp_Inv_MainInvoice"], "business_value": "تحديد أفضل المنتجات لتحسين استراتيجيات التسويق", "example_questions": ["ما هو أعلى منتج مبيعاً؟", "أيهما أكثر مبيعاً كولا أم فانتا؟", "ما المنتج الأكثر طلباً؟", "أي منتج حقق أعلى إيرادات؟"], "sql_template": "SELECT TOP 1 ItemName, SUM(Quantity) as TotalQuantity, SUM(Amount) as TotalRevenue FROM {table} WHERE DocumentName = 'فاتورة مبيعات' AND TheDate BETWEEN '{start_date}' AND '{end_date}' GROUP BY ItemName ORDER BY TotalQuantity DESC"}, {"id": "UC002", "name": "أكثر عشرة منتجات مبيعاً", "description": "عرض قائمة بأكثر عشرة منتجات مبيعاً في فترة زمنية معينة", "category": "تحليل المبيعات", "tables_involved": ["tbltemp_ItemsMain", "tbltemp_Inv_MainInvoice"], "business_value": "تحديد أفضل 10 منتجات لتحسين إدارة المخزون", "example_questions": ["ما أكثر عشرة منتجات مبيعاً؟", "أظهر أفضل 10 منتجات مبيعاً", "ما الأعلى 10 من حيث المبيعات؟", "أيهما أفضل 10 منتجات؟"], "sql_template": "SELECT TOP 10 ItemName, SUM(Quantity) as TotalQuantity, SUM(Amount) as TotalRevenue FROM {table} WHERE DocumentName = 'فاتورة مبيعات' AND TheDate BETWEEN '{start_date}' AND '{end_date}' GROUP BY ItemName ORDER BY TotalQuantity DESC"}, {"id": "UC003", "name": "أكثر منتج مبيعاً خلال يوم", "description": "تحديد المنتج الأكثر مبيعاً في يوم محدد", "category": "تحليل المبيعات اليومية", "tables_involved": ["tbltemp_ItemsMain", "tbltemp_Inv_MainInvoice"], "business_value": "تحديد المنتجات الأكثر طلباً يومياً لتحسين التخزين", "example_questions": ["ما أكثر منتج مبيعاً اليوم؟", "أيهما أكثر بيعاً اليوم كولا أم سبريت؟", "ما المنتج الأكثر طلباً اليوم؟", "كم بعنا من كل منتج اليوم؟"], "sql_template": "SELECT TOP 1 ItemName, SUM(Quantity) as TotalQuantity FROM {table} WHERE DocumentName = 'فاتورة مبيعات' AND CAST(TheDate as DATE) = '{specific_date}' GROUP BY ItemName ORDER BY TotalQuantity DESC"}, {"id": "UC004", "name": "أكثر منتج مبيعاً خلال أسبوع", "description": "تحديد المنتج الأكثر مبيعاً خلال أسبوع محدد", "category": "تحليل المبيعات الأسبوعية", "tables_involved": ["tbltemp_ItemsMain", "tbltemp_Inv_MainInvoice"], "business_value": "تحليل أداء المنتجات أسبوعياً لتحسين التخطيط", "example_questions": ["ما أكثر منتج مبيعاً هذا الأسبوع؟", "أيهما أكثر بيعاً الأسبوع الماضي؟", "ما المنتج الأكثر طلباً هذا الأسبوع؟", "كم بعنا من كل منتج هذا الأسبوع؟"], "sql_template": "SELECT TOP 1 ItemName, SUM(Quantity) as TotalQuantity FROM {table} WHERE DocumentName = 'فاتورة مبيعات' AND TheDate >= DATEADD(week, -1, GETDATE()) GROUP BY ItemName ORDER BY TotalQuantity DESC"}, {"id": "UC005", "name": "أكثر منتج مبيعاً خلال شهر", "description": "تحديد المنتج الأكثر مبيعاً خلال شهر محدد", "category": "تحليل المبيعات الشهرية", "tables_involved": ["tbltemp_ItemsMain", "tbltemp_Inv_MainInvoice"], "business_value": "تحليل أداء المنتجات شهرياً لتحسين التخطيط", "example_questions": ["ما أكثر منتج مبيعاً هذا الشهر؟", "أيهما أكثر بيعاً الشهر الماضي؟", "ما المنتج الأكثر طلباً يناير؟", "كم بعنا من كل منتج هذا الشهر؟"], "sql_template": "SELECT TOP 1 ItemName, SUM(Quantity) as TotalQuantity FROM {table} WHERE DocumentName = 'فاتورة مبيعات' AND MONTH(TheDate) = {month} AND YEAR(TheDate) = {year} GROUP BY ItemName ORDER BY TotalQuantity DESC"}, {"id": "UC006", "name": "أكثر منتج مبيعاً خلال سنة", "description": "تحديد المنتج الأكثر مبيعاً خلال سنة محددة", "category": "تحليل المبيعات السنوية", "tables_involved": ["tbltemp_ItemsMain", "tbltemp_Inv_MainInvoice"], "business_value": "تحليل أداء المنتجات سنوياً لتحسين التخطيط الاستراتيجي", "example_questions": ["ما أكثر منتج مبيعاً هذا العام؟", "أيهما أكثر بيعاً العام الماضي؟", "ما المنتج الأكثر طلباً 2024؟", "كم بعنا من كل منتج هذا العام؟"], "sql_template": "SELECT TOP 1 ItemName, SUM(Quantity) as TotalQuantity FROM {table} WHERE DocumentName = 'فاتورة مبيعات' AND YEAR(TheDate) = {year} GROUP BY ItemName ORDER BY TotalQuantity DESC"}, {"id": "UC007", "name": "العميل الأكثر شراءً", "description": "تحديد العميل الذي أنفق أكثر في فترة زمنية معينة", "category": "تحليل العملاء", "tables_involved": ["tbltemp_ItemsMain", "tbltemp_Inv_MainInvoice"], "business_value": "تحديد أفضل العملاء لبرامج الولاء", "example_questions": ["من هو العميل الأكثر شراءً؟", "من أنفق أكثر من كل الزبائن؟", "من هو أفضل عميل لنا؟", "من أكثر العملاء ولاءً؟"], "sql_template": "SELECT TOP 1 ClientName, SUM(Amount) as TotalSpent, COUNT(*) as OrderCount FROM {table} WHERE DocumentName = 'فاتورة مبيعات' AND TheDate BETWEEN '{start_date}' AND '{end_date}' GROUP BY ClientName ORDER BY TotalSpent DESC"}, {"id": "UC008", "name": "العميل الأكثر شراءً خلال يوم", "description": "تحديد العميل الذي أنفق أكثر في يوم محدد", "category": "تحليل العملاء اليومية", "tables_involved": ["tbltemp_ItemsMain", "tbltemp_Inv_MainInvoice"], "business_value": "تحديد العملاء النشطين يومياً", "example_questions": ["من هو العميل الأكثر شراءً اليوم؟", "من أنفق أكثر اليوم؟", "من هو أفضل عميل اليوم؟", "من زارنا اليوم وأنفق أكثر؟"], "sql_template": "SELECT TOP 1 ClientName, SUM(Amount) as TotalSpent FROM {table} WHERE DocumentName = 'فاتورة مبيعات' AND CAST(TheDate as DATE) = '{specific_date}' GROUP BY ClientName ORDER BY TotalSpent DESC"}, {"id": "UC009", "name": "العميل الأكثر شراءً خلال أسبوع", "description": "تحديد العميل الذي أنفق أكثر خلال أسبوع محدد", "category": "تحليل العملاء الأسبوعية", "tables_involved": ["tbltemp_ItemsMain", "tbltemp_Inv_MainInvoice"], "business_value": "تحديد العملاء النشطين أسبوعياً", "example_questions": ["من هو العميل الأكثر شراءً هذا الأسبوع؟", "من أنفق أكثر الأسبوع الماضي؟", "من هو أفضل عميل هذا الأسبوع؟", "من أكثر العملاء نشاطاً هذا الأسبوع؟"], "sql_template": "SELECT TOP 1 ClientName, SUM(Amount) as TotalSpent FROM {table} WHERE DocumentName = 'فاتورة مبيعات' AND TheDate >= DATEADD(week, -1, GETDATE()) GROUP BY ClientName ORDER BY TotalSpent DESC"}, {"id": "UC010", "name": "العميل الأكثر شراءً خلال شهر", "description": "تحديد العميل الذي أنفق أكثر خلال شهر محدد", "category": "تحليل العملاء الشهرية", "tables_involved": ["tbltemp_ItemsMain", "tbltemp_Inv_MainInvoice"], "business_value": "تحديد العملاء النشطين شهرياً", "example_questions": ["من هو العميل الأكثر شراءً هذا الشهر؟", "من أنفق أكثر يناير؟", "من هو أفضل عميل هذا الشهر؟", "من أكثر العملاء نشاطاً يناير؟"], "sql_template": "SELECT TOP 1 ClientName, SUM(Amount) as TotalSpent FROM {table} WHERE DocumentName = 'فاتورة مبيعات' AND MONTH(TheDate) = {month} AND YEAR(TheDate) = {year} GROUP BY ClientName ORDER BY TotalSpent DESC"}, {"id": "UC011", "name": "العميل الأكثر شراءً خلال سنة", "description": "تحديد العميل الذي أنفق أكثر خلال سنة محددة", "category": "تحليل العملاء السنوية", "tables_involved": ["tbltemp_ItemsMain", "tbltemp_Inv_MainInvoice"], "business_value": "تحديد العملاء الأكثر ولاءً سنوياً", "example_questions": ["من هو العميل الأكثر شراءً هذا العام؟", "من أنفق أكثر 2024؟", "من هو أفضل عميل هذا العام؟", "من أكثر العملاء ولاءً 2024؟"], "sql_template": "SELECT TOP 1 ClientName, SUM(Amount) as TotalSpent FROM {table} WHERE DocumentName = 'فاتورة مبيعات' AND YEAR(TheDate) = {year} GROUP BY ClientName ORDER BY TotalSpent DESC"}, {"id": "UC012", "name": "مقارنة بين العملاء", "description": "مقارنة إنفاق عملاء مختلفين لتحديد الأفضل", "category": "تحليل العملاء", "tables_involved": ["tbltemp_ItemsMain", "tbltemp_Inv_MainInvoice"], "business_value": "تحديد العملاء الأكثر قيمة وتحسين خدمة العملاء", "example_questions": ["من أفضل عميل محمد أم أحمد؟", "أيهما أنفق أكثر العميل الأول أم الثاني؟", "قارن بين عملاء فلان وعلان", "من أكثر ولاءً بين هؤلاء العملاء؟"], "sql_template": "SELECT ClientName, SUM(Amount) as TotalSpent FROM {table} WHERE DocumentName = 'فاتورة مبيعات' AND ClientName IN ({client_list}) AND TheDate BETWEEN '{start_date}' AND '{end_date}' GROUP BY ClientName ORDER BY TotalSpent DESC"}, {"id": "UC013", "name": "مقارنة بين الفروع", "description": "مقارنة أداء فروع مختلفة لتحديد الأفضل", "category": "تحليل الأداء", "tables_involved": ["tbltemp_ItemsMain", "tbltemp_Inv_MainInvoice"], "business_value": "تحديد الفروع الأكثر كفاءة وتوجيه الموارد", "example_questions": ["أيهما أفضل فرع صنعاء أم عدن؟", "قارن بين فرع الأول وفرع الثاني", "من أكثر فرعاً مبيعاً صنعاء أم حضرموت؟", "أيهما أكثر إنتاجية بين الفروع؟"], "sql_template": "SELECT BranchName, SUM(Amount) as TotalSales, COUNT(*) as TransactionCount FROM {table} WHERE DocumentName = 'فاتورة مبيعات' AND BranchName IN ({branch_list}) AND TheDate BETWEEN '{start_date}' AND '{end_date}' GROUP BY BranchName ORDER BY TotalSales DESC"}, {"id": "UC014", "name": "أعلى الفروع مبيعاً", "description": "تحديد الفرع الذي حقق أعلى مبيعات في فترة زمنية معينة", "category": "تحليل الأداء", "tables_involved": ["tbltemp_ItemsMain", "tbltemp_Inv_MainInvoice"], "business_value": "تحديد الفروع الأكثر كفاءة لتحسين الأداء", "example_questions": ["ما أعلى فرع مبيعاً؟", "أيهما أكثر فرعاً مبيعاً؟", "من أفضل فرع لنا؟", "أي فرع حقق أعلى إيرادات؟"], "sql_template": "SELECT TOP 1 BranchName, SUM(Amount) as TotalSales FROM {table} WHERE DocumentName = 'فاتورة مبيعات' AND TheDate BETWEEN '{start_date}' AND '{end_date}' GROUP BY BranchName ORDER BY TotalSales DESC"}, {"id": "UC015", "name": "أعلى الفروع مبيعاً خلال يوم", "description": "تحديد الفرع الأكثر مبيعاً في يوم محدد", "category": "تحليل الأداء اليومي", "tables_involved": ["tbltemp_ItemsMain", "tbltemp_Inv_MainInvoice"], "business_value": "تحديد الفروع الأكثر نشاطاً يومياً", "example_questions": ["ما أعلى فرع مبيعاً اليوم؟", "أيهما أكثر فرعاً مبيعاً اليوم؟", "من أفضل فرع اليوم؟", "أي فرع حقق أعلى إيرادات اليوم؟"], "sql_template": "SELECT TOP 1 BranchName, SUM(Amount) as TotalSales FROM {table} WHERE DocumentName = 'فاتورة مبيعات' AND CAST(TheDate as DATE) = '{specific_date}' GROUP BY BranchName ORDER BY TotalSales DESC"}, {"id": "UC016", "name": "أعلى الفروع مبيعاً خلال أسبوع", "description": "تحديد الفرع الأكثر مبيعاً خلال أسبوع محدد", "category": "تحليل الأداء الأسبوعي", "tables_involved": ["tbltemp_ItemsMain", "tbltemp_Inv_MainInvoice"], "business_value": "تحديد الفروع الأكثر نشاطاً أسبوعياً", "example_questions": ["ما أعلى فرع مبيعاً هذا الأسبوع؟", "أيهما أكثر فرعاً مبيعاً الأسبوع الماضي؟", "من أفضل فرع هذا الأسبوع؟", "أي فرع حقق أعلى إيرادات هذا الأسبوع؟"], "sql_template": "SELECT TOP 1 BranchName, SUM(Amount) as TotalSales FROM {table} WHERE DocumentName = 'فاتورة مبيعات' AND TheDate >= DATEADD(week, -1, GETDATE()) GROUP BY BranchName ORDER BY TotalSales DESC"}, {"id": "UC017", "name": "أعلى الفروع مبيعاً خلال شهر", "description": "تحديد الفرع الأكثر مبيعاً خلال شهر محدد", "category": "تحليل الأداء الشهري", "tables_involved": ["tbltemp_ItemsMain", "tbltemp_Inv_MainInvoice"], "business_value": "تحديد الفروع الأكثر نشاطاً شهرياً", "example_questions": ["ما أعلى فرع مبيعاً هذا الشهر؟", "أيهما أكثر فرعاً مبيعاً يناير؟", "من أفضل فرع هذا الشهر؟", "أي فرع حقق أعلى إيرادات يناير؟"], "sql_template": "SELECT TOP 1 BranchName, SUM(Amount) as TotalSales FROM {table} WHERE DocumentName = 'فاتورة مبيعات' AND MONTH(TheDate) = {month} AND YEAR(TheDate) = {year} GROUP BY BranchName ORDER BY TotalSales DESC"}, {"id": "UC018", "name": "أعلى الفروع مبيعاً خلال سنة", "description": "تحديد الفرع الأكثر مبيعاً خلال سنة محددة", "category": "تحليل الأداء السنوي", "tables_involved": ["tbltemp_ItemsMain", "tbltemp_Inv_MainInvoice"], "business_value": "تحديد الفروع الأكثر كفاءة سنوياً", "example_questions": ["ما أعلى فرع مبيعاً هذا العام؟", "أيهما أكثر فرعاً مبيعاً 2024؟", "من أفضل فرع هذا العام؟", "أي فرع حقق أعلى إيرادات 2024؟"], "sql_template": "SELECT TOP 1 BranchName, SUM(Amount) as TotalSales FROM {table} WHERE DocumentName = 'فاتورة مبيعات' AND YEAR(TheDate) = {year} GROUP BY BranchName ORDER BY TotalSales DESC"}, {"id": "UC019", "name": "مقارنة الموردين", "description": "مقارنة أداء الموردين المختلفين", "category": "تحليل المشتريات", "tables_involved": ["tbltemp_Inv_MainInvoice"], "business_value": "تحديد أفضل الموردين لتحسين سلسلة التوريد", "example_questions": ["من أفضل مورد XYZ أم ABC؟", "قارن بين موردين فلان وعلان", "أيهما أكثر موثوقية بين الموردين؟", "من قدم أفضل أسعار بين الموردين؟"], "sql_template": "SELECT DistributorName, SUM(TotalAmount) as TotalPurchased, COUNT(*) as OrderCount FROM {table} WHERE DocumentName = 'فاتورة مشتريات' AND DistributorName IN ({supplier_list}) AND TheDate BETWEEN '{start_date}' AND '{end_date}' GROUP BY DistributorName ORDER BY TotalPurchased DESC"}, {"id": "UC020", "name": "أعلى مورد مبيعاً", "description": "تحديد المورد الذي قدم أعلى مشتريات في فترة زمنية معينة", "category": "تحليل المشتريات", "tables_involved": ["tbltemp_Inv_MainInvoice"], "business_value": "تحديد أفضل الموردين لتطوير علاقات استراتيجية", "example_questions": ["من هو أعلى مورد مبيعاً؟", "أيهما أكثر مورداً مبيعاً؟", "من أفضل مورد لنا؟", "أي مورد قدم أعلى قيمة مشتريات؟"], "sql_template": "SELECT TOP 1 DistributorName, SUM(TotalAmount) as TotalPurchased FROM {table} WHERE DocumentName = 'فاتورة مشتريات' AND TheDate BETWEEN '{start_date}' AND '{end_date}' GROUP BY DistributorName ORDER BY TotalPurchased DESC"}]}