{"table": "tbltemp_Inv_MainInvoice", "description": "هذا الجدول يحتوي على تفاصيل الفواتير الرئيسية، بما في ذلك معلومات حول المنتجات المباعة، الأسعار، الكميات، والعملاء. يتم استخدامه لتتبع المعاملات المالية والمخزون.", "fields": {"ID": "رقم التسلسلي الفريد لكل سجل في الجدول.", "DocumentName": "اسم الوثيقة المرتبطة بالفاتورة.", "RecordID": "رقم التسلسلي للسجل في الوثيقة المرتبطة.", "TheNumber": "رقم الفاتورة.", "SupplierName": "اسم المورد المرتبط بالفاتورة.", "InvoiceID": "رقم التسلسلي للفاتورة.", "DetailsID": "رقم التسلسلي لتفاصيل الفاتورة.", "TheDate": "تاريخ إصدار الفاتورة.", "EnterTime": "وقت إدخال الفاتورة في النظام.", "ItemID": "رقم التسلسلي للمنتج المباع.", "ItemName": "اسم المنتج المباع.", "UnitID": "رقم التسلسلي للوحدة المستخدمة لقياس المنتج.", "UnitPrice": "سعر الوحدة للمنتج المباع.", "Quantity": "كمية المنتج المباع.", "Bonus": "كمية الهدية أو البونص للمنتج.", "TotalAmount": "المبلغ الإجمالي للفاتورة.", "MainUnitQuantity": "كمية المنتج بالوحدة الرئيسية.", "MainUnitPrice": "سعر الوحدة الرئيسية للمنتج.", "MainUnitID": "رقم التسلسلي للوحدة الرئيسية.", "MainUnitBonus": "كمية الهدية بالوحدة الرئيسية.", "StoreID": "رقم التسلسلي للمخزن الذي تم فيه إجراء المعاملة.", "BranchID": "رقم التسلسلي للفرع الذي تم فيه إجراء المعاملة.", "ExchangeFactor": "عامل التحويل بين الوحدات المختلفة للمنتج.", "ClientID": "رقم التسلسلي للعميل الذي تم إصدار الفاتورة له.", "ClientName": "اسم العميل الذي تم إصدار الفاتورة له.", "MCAmount": "المبلغ الإجمالي بالعملة الرئيسية.", "NewSubItemEntryID": "رقم التسلسلي للمنتج الفرعي الجديد المدخل في النظام.", "CurrencyID": "رقم التسلسلي للعملة المستخدمة في الفاتورة.", "TheMethod": "طريقة الدفع المستخدمة في الفاتورة.", "ExchangePrice": "سعر الصرف بين العملات المختلفة.", "TotalAmountByCurrencyInvetory": "المبلغ الإجمالي للفاتورة بالعملة المخزونية.", "DistributorID": "رقم التسلسلي للمورد المرتبط بالفاتورة.", "DistributorName": "اسم المورد المرتبط بالفاتورة.", "CostCenterID": "رقم التسلسلي لمركز التكلفة.", "CostCenterName": "اسم مركز التكلفة.", "ExpiryDate": "تاريخ انتهاء صلاحية المنتج."}, "use_cases": [{"name": "أكثر منتج مبيعاً في الفواتير", "description": "استعلام لعرض أكثر المنتجات مبيعاً حسب الكمية المباعة في الفواتير", "example_sql": "SELECT TOP 10 ItemName, SUM(Quantity) as TotalQuantity FROM tbltemp_Inv_MainInvoice WHERE DocumentName = 'فاتورة مبيعات' GROUP BY ItemName ORDER BY TotalQuantity DESC"}, {"name": "أعلى فواتير حسب سعر الوحدة", "description": "استعلام لعرض أعلى القيم في الفواتير مرتبة حسب سعر الوحدة", "example_sql": "SELECT TOP 10 ItemName, AVG(UnitPrice) as AveragePrice FROM tbltemp_Inv_MainInvoice GROUP BY ItemName ORDER BY AveragePrice DESC"}, {"name": "تحليل زمني للمبيعات في الفواتير", "description": "استعلام لتحليل المبيعات عبر الزمن حسب تاريخ الفاتورة", "example_sql": "SELECT YEAR(TheDate) as Year, MONTH(TheDate) as Month, SUM(TotalAmount) as TotalSales FROM tbltemp_Inv_MainInvoice WHERE DocumentName = 'فاتورة مبيعات' GROUP BY YEAR(TheDate), MONTH(TheDate) ORDER BY Year, Month"}, {"name": "أفضل العملاء من حيث الإنفاق في الفواتير", "description": "استعلام لعرض أفضل العملاء من حيث إجمالي الإنفاق في الفواتير", "example_sql": "SELECT TOP 10 ClientName, SUM(TotalAmount) as TotalSpent FROM tbltemp_Inv_MainInvoice WHERE DocumentName = 'فاتورة مبيعات' GROUP BY ClientName ORDER BY TotalSpent DESC"}, {"name": "أعلى الفروع مبيعاً في الفواتير", "description": "استعلام لعرض أعلى الفروع من حيث إجمالي المبيعات في الفواتير", "example_sql": "SELECT TOP 10 BranchID, SUM(TotalAmount) as TotalSales FROM tbltemp_Inv_MainInvoice WHERE DocumentName = 'فاتورة مبيعات' GROUP BY BranchID ORDER BY TotalSales DESC"}, {"name": "تحليل الفواتير حسب نوع الوثيقة", "description": "استعلام لتحليل الفواتير حسب نوع الوثيقة", "example_sql": "SELECT DocumentName, COUNT(*) as InvoiceCount, SUM(TotalAmount) as TotalAmount FROM tbltemp_Inv_MainInvoice GROUP BY DocumentName ORDER BY TotalAmount DESC"}, {"name": "الفواتير حسب طريقة الدفع", "description": "استعلام لتحليل الفواتير حسب طريقة الدفع المستخدمة", "example_sql": "SELECT TheMethod, COUNT(*) as InvoiceCount, SUM(TotalAmount) as TotalAmount FROM tbltemp_Inv_MainInvoice GROUP BY TheMethod ORDER BY TotalAmount DESC"}, {"name": "تحليل الموردين في الفواتير", "description": "استعلام لتحليل الموردين من حيث كمية المشتريات في الفواتير", "example_sql": "SELECT DistributorName, SUM(Quantity) as TotalPurchased, SUM(TotalAmount) as TotalAmount FROM tbltemp_Inv_MainInvoice WHERE DocumentName = 'فاتورة مشتريات' GROUP BY DistributorName ORDER BY TotalAmount DESC"}, {"name": "الفواتير حسب العملة", "description": "استعلام لتحليل الفواتير حسب العملة المستخدمة", "example_sql": "SELECT CurrencyID, COUNT(*) as InvoiceCount, SUM(MCAmount) as TotalAmount FROM tbltemp_Inv_MainInvoice GROUP BY CurrencyID ORDER BY TotalAmount DESC"}, {"name": "المنتجات الأكثر ربحاً في الفواتير", "description": "استعلام لحساب هامش الربح للمنتجات في الفواتير", "example_sql": "SELECT ItemName, SUM(TotalAmount) as TotalRevenue, SUM(MCAmount) as TotalCost, (SUM(TotalAmount) - SUM(MCAmount)) as Profit FROM tbltemp_Inv_MainInvoice GROUP BY ItemName HAVING SUM(TotalAmount) > 0 ORDER BY Profit DESC"}, {"name": "الفواتير حسب التاريخ", "description": "استعلام لتحليل عدد الفواتير وتOTAL المبالغ حسب التاريخ", "example_sql": "SELECT CAST(TheDate as DATE) as InvoiceDate, COUNT(*) as InvoiceCount, SUM(TotalAmount) as DailyTotal FROM tbltemp_Inv_MainInvoice GROUP BY CAST(TheDate as DATE) ORDER BY InvoiceDate DESC"}, {"name": "المنتجات حسب كمية البونص", "description": "استعلام لتحليل المنتجات حسب كمية الهدية أو البونص المقدمة", "example_sql": "SELECT ItemName, SUM(Bonus) as TotalBonus, SUM(Quantity) as TotalQuantity FROM tbltemp_Inv_MainInvoice WHERE Bonus > 0 GROUP BY ItemName ORDER BY TotalBonus DESC"}]}