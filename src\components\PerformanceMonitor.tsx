'use client';

import { useState, useEffect } from 'react';
import { Activity, Clock, Database, Zap, TrendingUp, AlertTriangle } from 'lucide-react';

interface PerformanceMetrics {
  queryCount: number;
  averageResponseTime: number;
  cacheHitRate: number;
  errorRate: number;
  activeConnections: number;
  memoryUsage: number;
  lastUpdated: string;
}

interface PerformanceMonitorProps {
  isVisible: boolean;
  onToggle: () => void;
}

export default function PerformanceMonitor({ isVisible, onToggle }: PerformanceMonitorProps) {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    queryCount: 0,
    averageResponseTime: 0,
    cacheHitRate: 0,
    errorRate: 0,
    activeConnections: 0,
    memoryUsage: 0,
    lastUpdated: new Date().toISOString()
  });

  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (isVisible) {
      loadMetrics();
      const interval = setInterval(loadMetrics, 30000); // تحديث كل 30 ثانية
      return () => clearInterval(interval);
    }
  }, [isVisible]);

  const loadMetrics = async () => {
    setIsLoading(true);
    try {
      // محاكاة تحميل الإحصائيات
      // في التطبيق الحقيقي، ستكون هذه استدعاءات API حقيقية
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockMetrics: PerformanceMetrics = {
        queryCount: Math.floor(Math.random() * 1000) + 100,
        averageResponseTime: Math.floor(Math.random() * 2000) + 500,
        cacheHitRate: Math.floor(Math.random() * 40) + 60,
        errorRate: Math.floor(Math.random() * 5),
        activeConnections: Math.floor(Math.random() * 10) + 1,
        memoryUsage: Math.floor(Math.random() * 30) + 40,
        lastUpdated: new Date().toISOString()
      };
      
      setMetrics(mockMetrics);
    } catch (error) {
      console.error('خطأ في تحميل إحصائيات الأداء:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusColor = (value: number, thresholds: { good: number; warning: number }) => {
    if (value >= thresholds.good) return 'text-green-600 dark:text-green-400';
    if (value >= thresholds.warning) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  const formatTime = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const formatLastUpdated = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('ar-SA');
  };

  if (!isVisible) {
    return (
      <button
        onClick={onToggle}
        className="fixed bottom-4 left-4 bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-full shadow-lg transition-colors z-40"
        title="عرض إحصائيات الأداء"
      >
        <Activity className="w-5 h-5" />
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 left-4 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 p-4 min-w-80 z-40">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <Activity className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            إحصائيات الأداء
          </h3>
        </div>
        <div className="flex items-center space-x-2 space-x-reverse">
          <button
            onClick={loadMetrics}
            disabled={isLoading}
            className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 disabled:opacity-50"
            title="تحديث"
          >
            <TrendingUp className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
          </button>
          <button
            onClick={onToggle}
            className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            title="إخفاء"
          >
            ×
          </button>
        </div>
      </div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-2 gap-3 mb-4">
        {/* Query Count */}
        <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
          <div className="flex items-center justify-between">
            <Database className="w-4 h-4 text-blue-500" />
            <span className="text-xs text-gray-500 dark:text-gray-400">الاستعلامات</span>
          </div>
          <div className="text-lg font-bold text-gray-900 dark:text-white mt-1">
            {metrics.queryCount.toLocaleString('ar-SA')}
          </div>
        </div>

        {/* Response Time */}
        <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
          <div className="flex items-center justify-between">
            <Clock className="w-4 h-4 text-green-500" />
            <span className="text-xs text-gray-500 dark:text-gray-400">زمن الاستجابة</span>
          </div>
          <div className={`text-lg font-bold mt-1 ${getStatusColor(2000 - metrics.averageResponseTime, { good: 1500, warning: 1000 })}`}>
            {formatTime(metrics.averageResponseTime)}
          </div>
        </div>

        {/* Cache Hit Rate */}
        <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
          <div className="flex items-center justify-between">
            <Zap className="w-4 h-4 text-purple-500" />
            <span className="text-xs text-gray-500 dark:text-gray-400">معدل التخزين المؤقت</span>
          </div>
          <div className={`text-lg font-bold mt-1 ${getStatusColor(metrics.cacheHitRate, { good: 80, warning: 60 })}`}>
            {metrics.cacheHitRate}%
          </div>
        </div>

        {/* Error Rate */}
        <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
          <div className="flex items-center justify-between">
            <AlertTriangle className="w-4 h-4 text-red-500" />
            <span className="text-xs text-gray-500 dark:text-gray-400">معدل الأخطاء</span>
          </div>
          <div className={`text-lg font-bold mt-1 ${getStatusColor(10 - metrics.errorRate, { good: 8, warning: 5 })}`}>
            {metrics.errorRate}%
          </div>
        </div>
      </div>

      {/* Additional Info */}
      <div className="space-y-2 text-xs text-gray-500 dark:text-gray-400">
        <div className="flex justify-between">
          <span>الاتصالات النشطة:</span>
          <span className="font-medium">{metrics.activeConnections}</span>
        </div>
        <div className="flex justify-between">
          <span>استخدام الذاكرة:</span>
          <span className="font-medium">{metrics.memoryUsage}%</span>
        </div>
        <div className="flex justify-between">
          <span>آخر تحديث:</span>
          <span className="font-medium">{formatLastUpdated(metrics.lastUpdated)}</span>
        </div>
      </div>

      {/* Status Indicator */}
      <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
        <div className="flex items-center">
          <div className={`w-2 h-2 rounded-full mr-2 ${
            metrics.errorRate < 2 && metrics.averageResponseTime < 1500 
              ? 'bg-green-500' 
              : metrics.errorRate < 5 && metrics.averageResponseTime < 3000
              ? 'bg-yellow-500'
              : 'bg-red-500'
          }`}></div>
          <span className="text-xs text-gray-600 dark:text-gray-400">
            {metrics.errorRate < 2 && metrics.averageResponseTime < 1500 
              ? 'النظام يعمل بشكل طبيعي' 
              : metrics.errorRate < 5 && metrics.averageResponseTime < 3000
              ? 'أداء متوسط'
              : 'يحتاج إلى تحسين'}
          </span>
        </div>
      </div>
    </div>
  );
}
