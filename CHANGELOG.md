# سجل التغييرات

جميع التغييرات المهمة في هذا المشروع سيتم توثيقها في هذا الملف.

التنسيق مبني على [Keep a Changelog](https://keepachangelog.com/ar/1.0.0/)،
وهذا المشروع يتبع [Semantic Versioning](https://semver.org/lang/ar/).

## [غير منشور]

### مضاف
- نظام إدارة الأخطاء المتقدم مع ErrorBoundary
- نظام الإشعارات التفاعلي مع دعم أنواع متعددة
- لوحة إعدادات شاملة مع إمكانية التصدير والاستيراد
- مراقب الأداء في الوقت الفعلي
- نظام التخزين المؤقت الذكي للاستعلامات
- تصنيف ذكي للأعمدة حسب الفئات
- اقتراحات استعلامات ذكية
- مكون الرسوم البيانية المتقدمة
- سكريبت اختبار الاتصال بقاعدة البيانات

### محسن
- تحسين خدمة الذكاء الاصطناعي مع تحليل أكثر دقة
- تطوير واجهة المستخدم مع دعم أفضل للغة العربية
- تحسين الأداء مع نظام التخزين المؤقت
- تحسين معالجة الأخطاء والتعافي منها

### مثبت
- إصلاح مشاكل الاتصال بقاعدة البيانات
- تحسين استقرار التطبيق
- إصلاح مشاكل التوافق مع المتصفحات المختلفة

## [1.0.0] - 2024-01-XX

### مضاف
- الإصدار الأولي من وكيل الذكاء الاصطناعي لـ SQL
- دعم اللغة العربية الطبيعية لتحويل الاستعلامات
- اتصال آمن بقواعد بيانات SQL Server
- فهرسة ذكية لمخطط قاعدة البيانات
- رسوم بيانية تفاعلية للنتائج
- تحليل ذكي للبيانات مع رؤى تجارية
- واجهة مستخدم حديثة ومتجاوبة
- دعم الوضع المظلم والفاتح
- تصدير النتائج بصيغ متعددة

### التقنيات المستخدمة
- Next.js 15 مع React 18
- TypeScript للأمان في الكتابة
- Tailwind CSS للتصميم
- SQL Server كقاعدة بيانات
- OpenRouter API للذكاء الاصطناعي
- Recharts للرسوم البيانية

### المتطلبات
- Node.js 18 أو أحدث
- SQL Server
- مفتاح API من OpenRouter

---

## أنواع التغييرات

- `مضاف` للميزات الجديدة
- `محسن` للتحسينات على الميزات الموجودة
- `مهمل` للميزات التي ستتم إزالتها قريباً
- `مزال` للميزات المزالة
- `مثبت` لإصلاح الأخطاء
- `أمان` في حالة الثغرات الأمنية
