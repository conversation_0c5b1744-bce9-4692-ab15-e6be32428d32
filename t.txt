عندما يبداء التطبيق لاول مره يطلب من المستخدم ادخال بيانات قاعده sql server 
ثم زر يتم التحقق من الاتصال ثم زر البداء عند الضغط عليه يتم استخراج كل الجدول والاعمده التي في قاعده البيانات 
والبداء في وصفها بمثل هذه الطريقة هذه الطريقة كمثال مثال فقط يجب ان تتعامل 
1. مثال هيكل الجدول (Schema) - بصيغة JSON مبسطة
json
نسخ
تحرير
{
  "tables": [
    {
      "name": "tbltemp_ItemsMain",
      "description": "جدول يحتوي على تفاصيل عناصر الفواتير مثل المبيعات والمشتريات.",
      "columns": [
        {
          "name": "ID",
          "type": "bigint",
          "description": "المعرف الأساسي لكل سجل"
        },
        {
          "name": "ItemName",
          "type": "varchar(200)",
          "description": "اسم المنتج أو الصنف"
        },
        {
          "name": "Quantity",
          "type": "numeric(18,6)",
          "description": "كمية المنتج المباعة أو المشتراة"
        },
        {
          "name": "TheDate",
          "type": "datetime",
          "description": "تاريخ العملية (بيع أو شراء)"
        },
        {
          "name": "DocumentName",
          "type": "varchar(150)",
          "description": "نوع الوثيقة (مثلاً فاتورة مبيعات أو فاتورة مشتريات)"
        },
        {
          "name": "BranchName",
          "type": "varchar(150)",
          "description": "اسم الفرع الذي تمت فيه العملية"
        },
        {
          "name": "ClientName",
          "type": "varchar(150)",
          "description": "اسم العميل في حالة البيع"
        },
        {
          "name": "UnitPrice",
          "type": "numeric(18,6)",
          "description": "سعر الوحدة للمنتج"
        },
        {
          "name": "Amount",
          "type": "numeric(18,6)",
          "description": "المبلغ الإجمالي للعملية"
        }
      ]
    }
  ]
}
2. مثال ملف التوصيف الذكي (Agent Profile) بالعربية
json
نسخ
تحرير
{
  "table": "tbltemp_ItemsMain",
  "description": "جدول يحتوي على تفاصيل الفواتير التي تشمل المبيعات والمشتريات، كل صف يمثل منتجًا في فاتورة محددة.",
  "fields": {
    "ItemName": "اسم المنتج أو الصنف الذي تم بيعه أو شراؤه",
    "Quantity": "كمية المنتج المتداولة في العملية",
    "TheDate": "تاريخ تنفيذ العملية",
    "DocumentName": "نوع الوثيقة، مثلاً 'فاتورة مبيعات' أو 'فاتورة مشتريات'",
    "BranchName": "الفرع الذي تمت فيه العملية",
    "ClientName": "اسم العميل الذي تم البيع له",
    "UnitPrice": "سعر الوحدة للمنتج",
    "Amount": "المبلغ الإجمالي للعملية"
  },
  "use_cases": [
    {
      "name": "أكثر منتج مبيعاً خلال فترة معينة",
      "description": "استعلام يعرض اسم المنتج الذي حقق أعلى كمية مباعة خلال فترة زمنية محددة.",
      "example_sql": "SELECT TOP 1 ItemName, SUM(Quantity) AS TotalQuantity FROM tbltemp_ItemsMain WHERE DocumentName = 'فاتورة مبيعات' AND TheDate BETWEEN @start_date AND @end_date GROUP BY ItemName ORDER BY TotalQuantity DESC"
    },
    {
      "name": "إجمالي المبيعات لكل فرع",
      "description": "استعلام لحساب مجموع المبيعات حسب الفرع خلال فترة معينة.",
      "example_sql": "SELECT BranchName, SUM(Amount) AS TotalSales FROM tbltemp_ItemsMain WHERE DocumentName = 'فاتورة مبيعات' AND TheDate BETWEEN @start_date AND @end_date GROUP BY BranchName ORDER BY TotalSales DESC"
    },
    {
      "name": "أكثر عميل شراءً في فرع معين",
      "description": "استعلام يحدد العميل الذي قام بأكبر حجم شراء في فرع معين خلال فترة زمنية محددة.",
      "example_sql": "SELECT TOP 1 ClientName, SUM(Amount) AS TotalAmount FROM tbltemp_ItemsMain WHERE DocumentName = 'فاتورة مبيعات' AND BranchName = @branch_name AND TheDate BETWEEN @start_date AND @end_date GROUP BY ClientName ORDER BY TotalAmount DESC"
    }
  ]
}
لا تنسى هذه امثله فقط ولكن انت يجب ان تتعمل مع كل الجدول والاعمده با احترافية هل فهمت 
وتميز متى تستخدم النيم ومتى تستخدم الاسم 
بعد ان يتم الاتصال بقعده البيانات يتم جلب وفهرسة الجدول والاعمده مثل الطريقة التي ذكرنا الذي يقوم بهذه هو النموذج اللغوي الكبير والذي سوف يكون عباره عن api 
OpenRouter
        model: "qwen/qwen-2.5-72b-instruct",
api key: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
هذه البيانات الافتراضية للاتصال بقاعده البيانات تكون في فورم ادخال بيانات قاعده البيانات يمكنن تغييرها ولكن تظل هي الافتراضية 
✅ التصنيف الذكي للأعمدة حسب نية الاستعلام:
🛍️ فئة: المنتجات والمبيعات
الاستعلامات المتوقعة	الأعمدة المهمة
أكثر منتج مبيعًا / أقل منتج	ItemID, ItemName, Quantity, Amount, Bonus
تفاصيل المنتج وسعره	ItemName, UnitPrice, ExchangeFactor, UnitID, UnitName, Barcode
المنتج حسب الصنف	CategoryID, CategoryName, ItemType, ItemTypeID
تواريخ الصلاحية	ExpiryDate, ISExpiry, ExpiryPoint

👥 فئة: العملاء والموردين
الاستعلامات المتوقعة	الأعمدة المهمة
أكثر عميل شراء	ClientID, ClientName, Amount, Quantity, BranchID
أكثر موزع / مورد	DistributorID, DistributorName, Amount, Quantity
العميل حسب المدينة أو الفرع	ClientID, BranchID, BranchName

📦 فئة: المخزون والمستودعات
الاستعلامات المتوقعة	الأعمدة المهمة
الكمية المتوفرة	StoreID, StoreName, Quantity, Bonus
مبيعات مستودع معين	StoreID, BranchID, Amount, Quantity
الفروع الأعلى مبيعًا	BranchID, BranchName, Amount

💵 فئة: القيم والمبالغ
الاستعلامات المتوقعة	الأعمدة المهمة
إجمالي المبيعات	Amount, MCAmount, TotalAmount, Bonus
مبيعات بعملة معينة	CurrencyID, CurrencyName, MCAmountCurrencyMain, ExchangePrice
الخصومات	Discount, ItemDiscount, McItemDiscount

🕒 فئة: التواريخ والفترات
الاستعلامات المتوقعة	الأعمدة المهمة
مبيعات هذا الأسبوع	TheDate, EnterTime, TheYear, ExpiryDate
مشتريات الشهر الماضي	TheDate, DocumentName (يُستخدم لتحديد نوع المستند)

📄 فئة: المستندات والفواتير
الاستعلامات المتوقعة	الأعمدة المهمة
تحليل نوع الفاتورة	DocumentName, DocumentID, RecordNumber, InvoiceID
تتبع الفاتورة	TheNumber, RecordID, DetailsID, SerialNumber

🧾 فئة: القيود والمحاسبة
الاستعلامات المتوقعة	الأعمدة المهمة
تصنيف محاسبي	AccountID, AccountName, AccountNumber, AccountFatherNumber
حسب مركز التكلفة	CostCenterID, CostCenterName, CostCenterNumber

✅ ملخص فئات الأعمدة بشكل مبسط:
الفئة	وصف مختصر
المنتجات	أي شيء يخص الصنف والكمية والسعر والوحدة
العملاء	هوية العميل والموزع وسجل الشراء
المخازن	المستودعات، الفروع، والمخزون
المبالغ	كل ما يتعلق بالقيمة، السعر، الخصومات
التواريخ	لتصفية الفترات وتحليل الأداء
الفواتير	المستندات والرقم التسلسلي
المحاسبة	الحسابات ومراكز التكلفة








# تحليل معمق لنظام وكيل الذكاء الاصطناعي المتصل بقاعدة بيانات SQL Server

## فهم بنية النظام المقترح

سأقوم بشرح تفصيلي لبنية النظام الذي تريده، مع التركيز على تدفق البيانات والطبقات الوظيفية دون الدخول في الأكواد.

### 1. الفئات الرئيسية للبيانات (كما طلبت)

لقد قمت بتقسيم الأعمدة إلى فئات منطقية بناءً على الغرض منها:

#### 🛍️ فئة المنتجات والمبيعات:
- **الأعمدة الأساسية**: ItemID, ItemName, Quantity, Amount, Bonus
- **التحليلات المتوقعة**: تحليل المبيعات حسب المنتج، تحديد المنتجات الأكثر ربحية، تحليل علاقة الكمية بالمكافآت

#### 👥 فئة العملاء والموردين:
- **الأعمدة الأساسية**: ClientID, ClientName, DistributorID, DistributorName
- **التحليلات المتوقعة**: تحليل سلوك العملاء، تحديد أفضل الموزعين، تحليل القيمة الدائمة للعميل

#### 📦 فئة المستودعات والفروع:
- **الأعمدة الأساسية**: StoreID, StoreName, BranchID, BranchName
- **التحليلات المتوقعة**: تحليل الأداء حسب الفرع، تحسين توزيع المخزون، تحديد المستودعات الأكثر كفاءة

#### 💵 فئة المبالغ والأسعار:
- **الأعمدة الأساسية**: Amount, MCAmount, ExchangePrice, Discount
- **التحليلات المتوقعة**: تحليل الهوامش الربحية، تأثير الخصومات على المبيعات، تحليل التقلبات السعرية

#### 🕒 فئة التواريخ والتحليل الزمني:
- **الأعمدة الأساسية**: TheDate, EnterTime, TheYear
- **التحليلات المتوقعة**: تحليل الاتجاهات الموسمية، نمو المبيعات السنوي، تحليل أنماط الشراء خلال اليوم

#### 📄 فئة الفواتير والمستندات:
- **الأعمدة الأساسية**: DocumentName, InvoiceID, TheNumber
- **التحليلات المتوقعة**: تتبع سلسلة المعاملات، تحليل دورة المستندات، تحديد الأنماط في إصدار الفواتير

#### 🧾 فئة المحاسبة ومراكز التكلفة:
- **الأعمدة الأساسية**: CostCenterID, AccountID, AccountNumber
- **التحليلات المتوقعة**: تحليل التكاليف حسب مركز التكلفة، تقييم الأداء المالي، تحليل الربحية

## تدفق العمل (Workflow) المفصل

### 1. مرحلة استقبال المدخلات (Input Reception)
- **المسؤول**: واجهة المستخدم (Next.js) + الوكيل الذكي
- **العملية**:
  - يستقبل الوكيل استعلام المستخدم الطبيعي (مثال: "أعرض لي أكثر 3 منتجات مبيعاً في الشهر الماضي")
  - يقوم الوكيل بتحليل أولي للسياق لتحديد نية المستخدم
  - يرسل الاستعلام إلى النموذج اللغوي مع المعلومات السياقية

### 2. مرحلة فهم النية وتحويلها إلى استعلام (Intent Understanding)
- **المسؤول**: النموذج اللغوي الكبير (مثل GPT)
- **العملية**:
  - يحلل النموذج اللغوي الاستعلام الطبيعي لتحديد:
    - نوع التحليل المطلوب (مقارنة، ترتيب، تجميع...)
    - الكيانات الرئيسية (منتجات، عملاء، فترات زمنية...)
    - الشروط والقيود (فترات زمنية، فلاتر محددة)
  - يحدد الجداول والأعمدة ذات الصلة (في حالتك tbltemp_ItemsMain و tbltemp_Inv_MainInvoice)
  - يولد "برومبت تحليلي" يحتوي على:
    - تفسير لنية المستخدم
    - الجداول والأعمدة المقترحة
    - نوع التحليل المطلوب
    - التنسيق المطلوب للنتائج (جدول، رسم بياني...)

### 3. مرحلة توليد الاستعلام وتنفيذه (Query Generation & Execution)
- **المسؤول**: الوكيل الذكي (باستخدام LangChain)
- **العملية**:
  - يستلم الوكيل البرومبت التحليلي من النموذج اللغوي
  - يتحقق من صحة وجدوى الأعمدة والجداول المقترحة
  - يبني استعلام SQL الأمثل مع مراعاة:
    - أداء قاعدة البيانات
    - الفلاتر الضرورية
    - تجميع البيانات عند الحاجة
  - ينفذ الاستعلام على SQL Server
  - يتعامل مع الأخطاء المحتملة (مثل أعمدة غير موجودة)

### 4. مرحلة تحليل النتائج وعرضها (Analysis & Visualization)
- **المسؤول**: النموذج اللغوي + الوكيل الذكي
- **العملية**:
  - يستلم الوكيل البيانات الخام من قاعدة البيانات
  - يرسلها إلى النموذج اللغوي مع السياق الأصلي للاستعلام
  - يقوم النموذج اللغوي بـ:
    - التحليل الإحصائي الأساسي
    - تحديد الأنماط والاتجاهات
    - توليـد رؤى قابلة للتنفيذ
    - إنشاء توصيات مبنية على البيانات
  - يقرر الشكل الأمثل لعرض البيانات (جدول، رسم بياني، نص تحليلي...)
  - يعيد النموذج اللغوي النتائج بشكل منظم للوكيل
  - يعرض الوكيل النتائج للمستخدم عبر واجهة Next.js

## الطبقات الرئيسية في النظام

### 1. طبقة واجهة المستخدم (Presentation Layer)
- **التقنيات**: Next.js
- **المسؤوليات**:
  - استقبال المدخلات الطبيعية من المستخدم
  - عرض النتائج بشكل جذاب (جداول، رسوم بيانية)
  - إدارة تفاعلات المستخدم

### 2. طبقة الوكيل الذكي (Agent Layer)
- **التقنيات**: LangChain
- **المسؤوليات**:
  - التنسيق بين طبقات النظام
  - إدارة المحادثة مع المستخدم
  - التحقق من صحة الاستعلامات
  - توليد وتنفيذ استعلامات SQL
  - إدارة السياق خلال الجلسة

### 3. طبقة النموذج اللغوي (LLM Layer)
- **المسؤوليات**:
  - فهم النوايا من الاستعلامات الطبيعية
  - تحويل الطلبات إلى برومبتات تحليلية
  - تحليل البيانات واستخراج الرؤى
  - صياغة الإجابات بلغة طبيعية

### 4. طبقة تكامل قاعدة البيانات (Data Integration Layer)
- **المسؤوليات**:
  - الاتصال الآمن بـ SQL Server
  - تنفيذ الاستعلامات بكفاءة
  - تحويل البيانات الخام إلى تنسيق قابل للتحليل

### 5. طبقة التحليلات (Analytics Layer)
- **المسؤوليات**:
  - التحليل الإحصائي
  - اكتشاف الأنماط
  - توليد التوصيات
  - تحديد أفضل طرق التصور

## كيف يفهم الوكيل الاستعلامات المختلفة؟

الوكيل يستخدم مجموعة من التقنيات لفهم الاستعلامات:

### 1. تحليل النية (Intent Analysis)
- **أمثلة**:
  - "أكثر المنتجات مبيعاً" → نية الترتيب التنازلي حسب الكمية
  - "مقارنة بين منتجين" → نية المقارنة بين كيانين
  - "اتجاهات المبيعات الشهرية" → نية التحليل الزمني

### 2. التعرف على الكيانات (Entity Recognition)
- تحديد الكيانات مثل:
  - أسماء المنتجات
  - فترات زمنية
  - معايير التصفية

### 3. فهم السياق (Context Understanding)
- يحتفظ الوكيل بسياق المحادثة لفهم الاستعلامات المتتابعة

### 4. التعلم من الأمثلة (Few-shot Learning)
- يستخدم النموذج اللغوي أمثلة لتحسين فهمه للاستعلامات المتخصصة

## معالجة الاستعلامات المعقدة - أمثلة عملية

### مثال 1: "أعرض أكثر 3 منتجات مبيعاً في الربع الأخير"
1. تحديد النية: ترتيب تنازلي + تصفية زمنية
2. تحديد الجداول: tbltemp_ItemsMain
3. تحديد الأعمدة: ItemName, Quantity, TheDate
4. بناء الاستعلام: 
   ```sql
   SELECT TOP 3 ItemName, SUM(Quantity) AS TotalQuantity 
   FROM tbltemp_ItemsMain 
   WHERE TheDate BETWEEN [start_date] AND [end_date] 
   GROUP BY ItemName 
   ORDER BY TotalQuantity DESC
   ```

### مثال 2: "قارن مبيعات الفرع الرئيسي والفرع الغربي في العام الماضي"
1. تحديد النية: مقارنة بين فروع + تصفية زمنية
2. تحديد الجداول: tbltemp_ItemsMain
3. تحديد الأعمدة: BranchName, Amount, TheDate
4. بناء الاستعلام:
   ```sql
   SELECT BranchName, SUM(Amount) AS TotalSales 
   FROM tbltemp_ItemsMain 
   WHERE TheDate BETWEEN [start_of_last_year] AND [end_of_last_year] 
   AND BranchName IN ('الرئيسي', 'الغربي') 
   GROUP BY BranchName
   ```

## ضمانات الجودة والأداء

1. **التحقق من صحة الاستعلام**: التأكد من أن الاستعلام المولد آمن وفعال
2. **التعامل مع الغموض**: طلب توضيح عند الحاجة
3. **تحسين الأداء**: استخدام الفهارس الموجودة في SQL Server
4. **إدارة الأخطاء**: تقديم تفسيرات مفهومة عند حدوث أخطاء

## التطور المستقبلي المحتمل

1. **التعلم التكيفي**: تحسين فهم الاستعلامات مع الوقت
2. **التنبؤات**: إضافة قدرات تنبؤية بناءً على البيانات التاريخية
3. **التكامل مع مصادر بيانات إضافية**: ربط النظام مع أنظمة أخرى
4. **التخصيص**: تكييف الإجابات حسب تفضيلات المستخدم

هذا النظام سيوفر واجهة طبيعية وقوية للتفاعل مع بياناتك، مع القدرة على استخراج رؤى عميقة بسهولة وسلاسة.


