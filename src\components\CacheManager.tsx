'use client';

import { useState, useEffect } from 'react';
import { Database, Trash2, RefreshCw, Search, Clock, BarChart3 } from 'lucide-react';

interface CacheStats {
  totalQueries: number;
  oldestQuery: string | null;
  newestQuery: string | null;
  totalSize: number;
  hitRate: number;
  averageExecutionTime: number;
}

interface CachedQuery {
  id: string;
  query: string;
  sql: string;
  timestamp: string;
  executionTime: number;
  dataHash: string;
}

export default function CacheManager() {
  const [stats, setStats] = useState<CacheStats | null>(null);
  const [popularQueries, setPopularQueries] = useState<CachedQuery[]>([]);
  const [searchResults, setSearchResults] = useState<CachedQuery[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'stats' | 'popular' | 'search'>('stats');

  useEffect(() => {
    loadCacheStats();
    loadPopularQueries();
  }, []);

  const loadCacheStats = async () => {
    try {
      const response = await fetch('/api/cache?action=stats');
      const result = await response.json();
      if (result.success) {
        setStats(result.data);
      }
    } catch (error) {
      console.error('خطأ في تحميل إحصائيات التخزين المؤقت:', error);
    }
  };

  const loadPopularQueries = async () => {
    try {
      const response = await fetch('/api/cache?action=popular&limit=10');
      const result = await response.json();
      if (result.success) {
        setPopularQueries(result.data);
      }
    } catch (error) {
      console.error('خطأ في تحميل الاستعلامات الشائعة:', error);
    }
  };

  const searchQueries = async () => {
    if (!searchTerm.trim()) return;
    
    setIsLoading(true);
    try {
      const response = await fetch(`/api/cache?action=search&search=${encodeURIComponent(searchTerm)}`);
      const result = await response.json();
      if (result.success) {
        setSearchResults(result.data);
      }
    } catch (error) {
      console.error('خطأ في البحث:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const cleanupCache = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/cache', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'cleanup' })
      });
      const result = await response.json();
      if (result.success) {
        await loadCacheStats();
        await loadPopularQueries();
        alert('تم تنظيف ذاكرة التخزين المؤقت بنجاح');
      }
    } catch (error) {
      console.error('خطأ في تنظيف التخزين المؤقت:', error);
      alert('فشل في تنظيف ذاكرة التخزين المؤقت');
    } finally {
      setIsLoading(false);
    }
  };

  const resetCache = async () => {
    if (!confirm('هل أنت متأكد من إعادة تعيين ذاكرة التخزين المؤقت؟ سيتم حذف جميع الاستعلامات المحفوظة.')) {
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('/api/cache', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'reset' })
      });
      const result = await response.json();
      if (result.success) {
        await loadCacheStats();
        await loadPopularQueries();
        setSearchResults([]);
        alert('تم إعادة تعيين ذاكرة التخزين المؤقت بنجاح');
      }
    } catch (error) {
      console.error('خطأ في إعادة تعيين التخزين المؤقت:', error);
      alert('فشل في إعادة تعيين ذاكرة التخزين المؤقت');
    } finally {
      setIsLoading(false);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleString('ar-SA');
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Database className="w-6 h-6 text-blue-600 dark:text-blue-400 mr-3" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            إدارة ذاكرة التخزين المؤقت
          </h2>
        </div>
        <div className="flex space-x-2 space-x-reverse">
          <button
            onClick={cleanupCache}
            disabled={isLoading}
            className="bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg transition-colors flex items-center"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            تنظيف
          </button>
          <button
            onClick={resetCache}
            disabled={isLoading}
            className="bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg transition-colors flex items-center"
          >
            <Trash2 className="w-4 h-4 mr-2" />
            إعادة تعيين
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700 mb-6">
        <nav className="-mb-px flex space-x-8 space-x-reverse">
          {[
            { id: 'stats', name: 'الإحصائيات', icon: BarChart3 },
            { id: 'popular', name: 'الاستعلامات الشائعة', icon: Clock },
            { id: 'search', name: 'البحث', icon: Search }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <tab.icon className="w-4 h-4 mr-2" />
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Stats Tab */}
      {activeTab === 'stats' && stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg">
            <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200">
              إجمالي الاستعلامات
            </h3>
            <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
              {stats.totalQueries}
            </p>
          </div>
          <div className="bg-green-50 dark:bg-green-900 p-4 rounded-lg">
            <h3 className="text-sm font-medium text-green-800 dark:text-green-200">
              حجم التخزين
            </h3>
            <p className="text-2xl font-bold text-green-900 dark:text-green-100">
              {formatFileSize(stats.totalSize)}
            </p>
          </div>
          <div className="bg-purple-50 dark:bg-purple-900 p-4 rounded-lg">
            <h3 className="text-sm font-medium text-purple-800 dark:text-purple-200">
              متوسط وقت التنفيذ
            </h3>
            <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
              {Math.round(stats.averageExecutionTime)}ms
            </p>
          </div>
          {stats.oldestQuery && (
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-gray-800 dark:text-gray-200">
                أقدم استعلام
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {formatDate(stats.oldestQuery)}
              </p>
            </div>
          )}
          {stats.newestQuery && (
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-gray-800 dark:text-gray-200">
                أحدث استعلام
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {formatDate(stats.newestQuery)}
              </p>
            </div>
          )}
        </div>
      )}

      {/* Popular Queries Tab */}
      {activeTab === 'popular' && (
        <div className="space-y-4">
          {popularQueries.length === 0 ? (
            <p className="text-gray-500 dark:text-gray-400 text-center py-8">
              لا توجد استعلامات محفوظة
            </p>
          ) : (
            popularQueries.map((query, index) => (
              <div key={query.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                      {query.query}
                    </h4>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                      {formatDate(query.timestamp)} • {query.executionTime}ms
                    </p>
                    <code className="text-xs bg-gray-100 dark:bg-gray-700 p-2 rounded block overflow-x-auto">
                      {query.sql}
                    </code>
                  </div>
                  <span className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 text-xs px-2 py-1 rounded">
                    #{index + 1}
                  </span>
                </div>
              </div>
            ))
          )}
        </div>
      )}

      {/* Search Tab */}
      {activeTab === 'search' && (
        <div>
          <div className="flex mb-4">
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="ابحث في الاستعلامات المحفوظة..."
              className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-r-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              onKeyPress={(e) => e.key === 'Enter' && searchQueries()}
            />
            <button
              onClick={searchQueries}
              disabled={isLoading || !searchTerm.trim()}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-l-lg transition-colors"
            >
              <Search className="w-4 h-4" />
            </button>
          </div>
          
          <div className="space-y-4">
            {searchResults.length === 0 && searchTerm ? (
              <p className="text-gray-500 dark:text-gray-400 text-center py-8">
                لم يتم العثور على نتائج
              </p>
            ) : (
              searchResults.map((query) => (
                <div key={query.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                    {query.query}
                  </h4>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                    {formatDate(query.timestamp)} • {query.executionTime}ms
                  </p>
                  <code className="text-xs bg-gray-100 dark:bg-gray-700 p-2 rounded block overflow-x-auto">
                    {query.sql}
                  </code>
                </div>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
}
