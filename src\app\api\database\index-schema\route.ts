import { NextRequest, NextResponse } from 'next/server';
import sql from 'mssql';
import fs from 'fs/promises';
import path from 'path';
import { findCachedDatabase, addDatabaseToCache } from '@/lib/database-cache';
import OpenAI from 'openai';

interface ConnectionData {
  server: string;
  database: string;
  username: string;
  password: string;
  port: string;
  trustServerCertificate: boolean;
}

interface TableSchema {
  name: string;
  description: string;
  columns: ColumnSchema[];
}

interface ColumnSchema {
  name: string;
  type: string;
  description: string;
  nullable: boolean;
  isPrimaryKey: boolean;
  isForeignKey: boolean;
}

// إعداد OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENROUTER_API_KEY || '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',
  baseURL: 'https://openrouter.ai/api/v1',
});

const MODEL_NAME = 'qwen/qwen-2.5-72b-instruct';

interface DatabaseSchema {
  tables: TableSchema[];
  intelligentClassification?: any;
}

export async function POST(request: NextRequest) {
  let pool: sql.ConnectionPool | null = null;
  
  try {
    const connectionData: ConnectionData = await request.json();

    // التحقق من وجود قاعدة البيانات في ذاكرة التخزين المؤقت
    const cachedDatabase = await findCachedDatabase(connectionData);

    if (cachedDatabase) {
      console.log('تم العثور على قاعدة البيانات في ذاكرة التخزين المؤقت');
      return NextResponse.json({
        success: true,
        message: 'تم تحميل قاعدة البيانات من ذاكرة التخزين المؤقت',
        schema: cachedDatabase.schema,
        tablesCount: cachedDatabase.schema.tables.length,
        columnsCount: cachedDatabase.schema.tables.reduce((sum: number, table: TableSchema) => sum + table.columns.length, 0),
        cached: true,
        indexed_at: cachedDatabase.indexed_at
      });
    }

    console.log('🔄 بدء فهرسة قاعدة بيانات جديدة...');

    // إعداد تكوين الاتصال
    const config: sql.config = {
      server: connectionData.server,
      database: connectionData.database,
      port: parseInt(connectionData.port) || 1433,
      options: {
        encrypt: true,
        trustServerCertificate: connectionData.trustServerCertificate,
        enableArithAbort: true,
      },
    };

    if (connectionData.username && connectionData.password) {
      config.user = connectionData.username;
      config.password = connectionData.password;
    } else {
      config.authentication = {
        type: 'ntlm',
        options: {
          domain: '',
          userName: '',
          password: '',
        },
      };
    }

    // الاتصال بقاعدة البيانات
    pool = await sql.connect(config);

    // استعلام للحصول على معلومات الجداول والأعمدة
    const tablesQuery = `
      SELECT 
        t.TABLE_NAME,
        t.TABLE_TYPE,
        c.COLUMN_NAME,
        c.DATA_TYPE,
        c.IS_NULLABLE,
        c.COLUMN_DEFAULT,
        c.CHARACTER_MAXIMUM_LENGTH,
        c.NUMERIC_PRECISION,
        c.NUMERIC_SCALE,
        CASE WHEN pk.COLUMN_NAME IS NOT NULL THEN 1 ELSE 0 END as IS_PRIMARY_KEY,
        CASE WHEN fk.COLUMN_NAME IS NOT NULL THEN 1 ELSE 0 END as IS_FOREIGN_KEY
      FROM INFORMATION_SCHEMA.TABLES t
      LEFT JOIN INFORMATION_SCHEMA.COLUMNS c ON t.TABLE_NAME = c.TABLE_NAME
      LEFT JOIN (
        SELECT ku.TABLE_NAME, ku.COLUMN_NAME
        FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
        JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku ON tc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
        WHERE tc.CONSTRAINT_TYPE = 'PRIMARY KEY'
      ) pk ON c.TABLE_NAME = pk.TABLE_NAME AND c.COLUMN_NAME = pk.COLUMN_NAME
      LEFT JOIN (
        SELECT ku.TABLE_NAME, ku.COLUMN_NAME
        FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
        JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku ON tc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
        WHERE tc.CONSTRAINT_TYPE = 'FOREIGN KEY'
      ) fk ON c.TABLE_NAME = fk.TABLE_NAME AND c.COLUMN_NAME = fk.COLUMN_NAME
      WHERE t.TABLE_TYPE = 'BASE TABLE'
      ORDER BY t.TABLE_NAME, c.ORDINAL_POSITION
    `;

    const result = await pool.request().query(tablesQuery);
    
    // تنظيم البيانات
    const tablesMap = new Map<string, TableSchema>();
    
    for (const row of result.recordset) {
      if (!tablesMap.has(row.TABLE_NAME)) {
        tablesMap.set(row.TABLE_NAME, {
          name: row.TABLE_NAME,
          description: `جدول ${row.TABLE_NAME}`,
          columns: []
        });
      }
      
      const table = tablesMap.get(row.TABLE_NAME)!;
      
      if (row.COLUMN_NAME) {
        const columnType = row.CHARACTER_MAXIMUM_LENGTH 
          ? `${row.DATA_TYPE}(${row.CHARACTER_MAXIMUM_LENGTH})`
          : row.NUMERIC_PRECISION 
          ? `${row.DATA_TYPE}(${row.NUMERIC_PRECISION},${row.NUMERIC_SCALE || 0})`
          : row.DATA_TYPE;

        table.columns.push({
          name: row.COLUMN_NAME,
          type: columnType,
          description: `عمود ${row.COLUMN_NAME} من نوع ${columnType}`,
          nullable: row.IS_NULLABLE === 'YES',
          isPrimaryKey: row.IS_PRIMARY_KEY === 1,
          isForeignKey: row.IS_FOREIGN_KEY === 1
        });
      }
    }

    const schema: DatabaseSchema = {
      tables: Array.from(tablesMap.values())
    };

    console.log('🤖 بدء التحليل الذكي باستخدام AI...');

    // 1. برومبت لوصف الجداول والأعمدة
    console.log('📋 تحسين أوصاف الجداول والأعمدة...');
    await enhanceTableDescriptions(schema);

    // 2. برومبت لإنشاء ملف النيات
    console.log('🎯 إنشاء ملف النيات...');
    await generateIntentsFile(schema);

    // 3. برومبت لإنشاء ملف الكيانات
    console.log('🏷️ إنشاء ملف الكيانات...');
    await generateEntitiesFile(schema);

    // 4. برومبت للتصنيف والفئات
    console.log('📂 إنشاء التصنيف والفئات...');
    schema.intelligentClassification = await generateClassificationFile(schema);

    // 5. إنشاء ملفات التوصيف لكل جدول
    console.log('📝 إنشاء ملفات التوصيف...');
    await generateTableProfiles(schema.tables);

    // حفظ المخطط المحسن
    const schemaPath = path.join(process.cwd(), 'src/data/database-schema.json');
    await fs.writeFile(schemaPath, JSON.stringify(schema, null, 2), 'utf-8');

    // حفظ قاعدة البيانات في الكاش
    await addDatabaseToCache(connectionData, schema);

    await pool.close();

    console.log('✅ تم إكمال الفهرسة الذكية بنجاح!');

    return NextResponse.json({
      success: true,
      message: 'تم فهرسة قاعدة البيانات بنجاح باستخدام الذكاء الاصطناعي',
      schema,
      tablesCount: schema.tables.length,
      columnsCount: schema.tables.reduce((sum, table) => sum + table.columns.length, 0),
      cached: false,
      indexed_at: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('خطأ في فهرسة قاعدة البيانات:', error);
    
    if (pool) {
      try {
        await pool.close();
      } catch (closeError) {
        console.error('خطأ في إغلاق الاتصال:', closeError);
      }
    }

    return NextResponse.json(
      {
        success: false,
        error: 'فشل في فهرسة قاعدة البيانات',
        details: error.message
      },
      { status: 500 }
    );
  }
}

// 1. برومبت لتحسين أوصاف الجداول والأعمدة
async function enhanceTableDescriptions(schema: DatabaseSchema) {
  try {
    const prompt = `
أنت خبير في تحليل قواعد البيانات التجارية. قم بتحليل قاعدة البيانات التالية وإنشاء أوصاف ذكية ومفيدة للجداول والأعمدة.

بيانات قاعدة البيانات:
${JSON.stringify(schema.tables.map((table: TableSchema) => ({
  name: table.name,
  columns: table.columns.map((col: ColumnSchema) => ({
    name: col.name,
    type: col.type,
    isPrimaryKey: col.isPrimaryKey,
    isForeignKey: col.isForeignKey,
    nullable: col.nullable
  }))
})), null, 2)}

المطلوب:
1. وصف ذكي لكل جدول يوضح الغرض منه ونوع البيانات التي يحتويها
2. وصف ذكي لكل عمود يوضح الغرض منه والبيانات التي يحتويها

أرجع النتيجة بصيغة JSON بالشكل التالي:
{
  "tables": [
    {
      "name": "اسم الجدول",
      "description": "وصف الجدول",
      "columns": [
        {
          "name": "اسم العمود",
          "description": "وصف العمود"
        }
      ]
    }
  ]
}

استخدم اللغة العربية في الأوصاف وكن دقيقاً ومفيداً.
`;

    const response = await openai.chat.completions.create({
      model: MODEL_NAME,
      messages: [
        {
          role: 'system',
          content: 'أنت خبير في تحليل قواعد البيانات التجارية. تقدم أوصاف دقيقة ومفيدة.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.1,
      max_tokens: 4000
    });

    const result = response.choices[0]?.message?.content;
    if (result) {
      const enhancedData = JSON.parse(result);

      // تطبيق الأوصاف المحسنة
      for (const enhancedTable of enhancedData.tables) {
        const originalTable = schema.tables.find((t: TableSchema) => t.name === enhancedTable.name);
        if (originalTable) {
          originalTable.description = enhancedTable.description;

          for (const enhancedColumn of enhancedTable.columns) {
            const originalColumn = originalTable.columns.find((c: ColumnSchema) => c.name === enhancedColumn.name);
            if (originalColumn) {
              originalColumn.description = enhancedColumn.description;
            }
          }
        }
      }
    }
  } catch (error) {
    console.error('خطأ في تحسين أوصاف الجداول:', error);
  }
}

// 2. برومبت لإنشاء ملف النيات
async function generateIntentsFile(schema: DatabaseSchema) {
  try {
    const prompt = `
أنت خبير في تحليل النيات للاستعلامات التجارية. قم بتحليل قاعدة البيانات التالية وإنشاء ملف النيات المناسب.

بيانات قاعدة البيانات:
${JSON.stringify(schema.tables.map((table: TableSchema) => ({
  name: table.name,
  description: table.description,
  columns: table.columns.map((col: ColumnSchema) => ({
    name: col.name,
    type: col.type,
    description: col.description
  }))
})), null, 2)}

المطلوب إنشاء ملف النيات يحتوي على:
1. النيات الأساسية للاستعلامات التجارية
2. الكلمات المفتاحية لكل نية
3. أمثلة على الاستعلامات
4. الجداول والأعمدة المرتبطة بكل نية

أرجع النتيجة بصيغة JSON بالشكل التالي:
{
  "intents": [
    {
      "name": "اسم النية",
      "description": "وصف النية",
      "keywords": ["كلمة1", "كلمة2"],
      "examples": ["مثال1", "مثال2"],
      "tables": ["جدول1", "جدول2"],
      "columns": ["عمود1", "عمود2"]
    }
  ]
}

ركز على النيات التجارية مثل: المبيعات، المشتريات، العملاء، المنتجات، التقارير، التحليلات.
`;

    const response = await openai.chat.completions.create({
      model: MODEL_NAME,
      messages: [
        {
          role: 'system',
          content: 'أنت خبير في تحليل النيات للاستعلامات التجارية باللغة العربية.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.1,
      max_tokens: 3000
    });

    const result = response.choices[0]?.message?.content;
    if (result) {
      const intentsData = JSON.parse(result);
      const intentsPath = path.join(process.cwd(), 'src/data/intents.json');
      await fs.writeFile(intentsPath, JSON.stringify(intentsData, null, 2), 'utf-8');
    }
  } catch (error) {
    console.error('خطأ في إنشاء ملف النيات:', error);
  }
}

// 3. برومبت لإنشاء ملف الكيانات
async function generateEntitiesFile(schema: DatabaseSchema) {
  try {
    const prompt = `
أنت خبير في تحليل الكيانات للاستعلامات التجارية. قم بتحليل قاعدة البيانات التالية وإنشاء ملف الكيانات المناسب.

بيانات قاعدة البيانات:
${JSON.stringify(schema.tables.map((table: TableSchema) => ({
  name: table.name,
  description: table.description,
  columns: table.columns.map((col: ColumnSchema) => ({
    name: col.name,
    type: col.type,
    description: col.description
  }))
})), null, 2)}

المطلوب إنشاء ملف الكيانات يحتوي على:
1. الكيانات الأساسية (المنتجات، العملاء، الفروع، إلخ)
2. القيم المحتملة لكل كيان
3. المرادفات والكلمات البديلة
4. الأعمدة المرتبطة بكل كيان

أرجع النتيجة بصيغة JSON بالشكل التالي:
{
  "entities": [
    {
      "name": "اسم الكيان",
      "description": "وصف الكيان",
      "type": "نوع الكيان",
      "values": ["قيمة1", "قيمة2"],
      "synonyms": ["مرادف1", "مرادف2"],
      "columns": ["عمود1", "عمود2"]
    }
  ]
}

ركز على الكيانات التجارية الأساسية.
`;

    const response = await openai.chat.completions.create({
      model: MODEL_NAME,
      messages: [
        {
          role: 'system',
          content: 'أنت خبير في تحليل الكيانات للاستعلامات التجارية باللغة العربية.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.1,
      max_tokens: 3000
    });

    const result = response.choices[0]?.message?.content;
    if (result) {
      const entitiesData = JSON.parse(result);
      const entitiesPath = path.join(process.cwd(), 'src/data/entities.json');
      await fs.writeFile(entitiesPath, JSON.stringify(entitiesData, null, 2), 'utf-8');
    }
  } catch (error) {
    console.error('خطأ في إنشاء ملف الكيانات:', error);
  }
}

// 4. برومبت للتصنيف والفئات
async function generateClassificationFile(schema: DatabaseSchema) {
  try {
    const prompt = `
أنت خبير في تصنيف البيانات التجارية. قم بتحليل قاعدة البيانات التالية وإنشاء تصنيف ذكي للأعمدة والجداول.

بيانات قاعدة البيانات:
${JSON.stringify(schema.tables.map((table: TableSchema) => ({
  name: table.name,
  description: table.description,
  columns: table.columns.map((col: ColumnSchema) => ({
    name: col.name,
    type: col.type,
    description: col.description
  }))
})), null, 2)}

المطلوب إنشاء تصنيف ذكي يحتوي على الفئات التالية:
1. المنتجات - كل ما يخص الأصناف والكمية والسعر والوحدة
2. العملاء - هوية العميل والموزع وسجل الشراء
3. المخازن - المستودعات، الفروع، والمخزون
4. المبالغ - كل ما يتعلق بالقيمة، السعر، الخصومات
5. التواريخ - لتصفية الفترات وتحليل الأداء
6. الفواتير - المستندات والرقم التسلسلي
7. المحاسبة - الحسابات ومراكز التكلفة

أرجع النتيجة بصيغة JSON بالشكل التالي:
{
  "المنتجات": {
    "description": "وصف الفئة",
    "columns": ["جدول.عمود1", "جدول.عمود2"],
    "tables": ["جدول1", "جدول2"]
  },
  "العملاء": {
    "description": "وصف الفئة",
    "columns": ["جدول.عمود1"],
    "tables": ["جدول1"]
  }
}
`;

    const response = await openai.chat.completions.create({
      model: MODEL_NAME,
      messages: [
        {
          role: 'system',
          content: 'أنت خبير في تصنيف البيانات التجارية باللغة العربية.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.1,
      max_tokens: 3000
    });

    const result = response.choices[0]?.message?.content;
    if (result) {
      return JSON.parse(result);
    }
    return {};
  } catch (error) {
    console.error('خطأ في إنشاء ملف التصنيف:', error);
    return {};
  }
}

// 5. دالة لإنشاء ملفات التوصيف لكل جدول
async function generateTableProfiles(tables: TableSchema[]) {
  try {
    // إنشاء مجلد profiles إذا لم يكن موجوداً
    const profilesDir = path.join(process.cwd(), 'src/data/profiles');
    try {
      await fs.access(profilesDir);
    } catch {
      await fs.mkdir(profilesDir, { recursive: true });
    }

    for (const table of tables) {
      console.log(`📋 إنشاء ملف توصيف لجدول: ${table.name}`);

      const profile = {
        table: table.name,
        description: table.description,
        fields: {} as Record<string, string>,
        use_cases: generateUseCases(table)
      };

      // إضافة أوصاف الأعمدة
      for (const column of table.columns) {
        profile.fields[column.name] = column.description;
      }

      // حفظ ملف التوصيف
      const profilePath = path.join(profilesDir, `${table.name}-profile.json`);
      await fs.writeFile(profilePath, JSON.stringify(profile, null, 2), 'utf-8');
    }
  } catch (error) {
    console.error('خطأ في إنشاء ملفات التوصيف:', error);
  }
}

function generateUseCases(table: TableSchema) {
  const useCases = [];

  // البحث عن أعمدة مهمة
  const nameColumns = table.columns.filter(col =>
    col.name.toLowerCase().includes('name') ||
    col.name.toLowerCase().includes('اسم') ||
    col.name.toLowerCase().includes('item')
  );

  const amountColumns = table.columns.filter(col =>
    col.name.toLowerCase().includes('amount') ||
    col.name.toLowerCase().includes('مبلغ') ||
    col.name.toLowerCase().includes('price') ||
    col.name.toLowerCase().includes('سعر') ||
    col.name.toLowerCase().includes('total')
  );

  const dateColumns = table.columns.filter(col =>
    col.type.toLowerCase().includes('date') ||
    col.type.toLowerCase().includes('time') ||
    col.name.toLowerCase().includes('date')
  );

  const quantityColumns = table.columns.filter(col =>
    col.name.toLowerCase().includes('quantity') ||
    col.name.toLowerCase().includes('كمية')
  );

  // إنشاء حالات استخدام متقدمة
  if (nameColumns.length > 0 && quantityColumns.length > 0) {
    useCases.push({
      name: `أكثر ${nameColumns[0].name} مبيعاً`,
      description: `استعلام لعرض أكثر العناصر في ${nameColumns[0].name} مبيعاً حسب ${quantityColumns[0].name}`,
      example_sql: `SELECT TOP 10 ${nameColumns[0].name}, SUM(${quantityColumns[0].name}) as TotalQuantity FROM ${table.name} WHERE DocumentName = 'فاتورة مبيعات' GROUP BY ${nameColumns[0].name} ORDER BY TotalQuantity DESC`
    });
  }

  if (nameColumns.length > 0 && amountColumns.length > 0) {
    useCases.push({
      name: `أعلى ${nameColumns[0].name} حسب ${amountColumns[0].name}`,
      description: `استعلام لعرض أعلى القيم في ${nameColumns[0].name} مرتبة حسب ${amountColumns[0].name}`,
      example_sql: `SELECT TOP 10 ${nameColumns[0].name}, SUM(${amountColumns[0].name}) as Total FROM ${table.name} GROUP BY ${nameColumns[0].name} ORDER BY Total DESC`
    });
  }

  if (dateColumns.length > 0 && amountColumns.length > 0) {
    useCases.push({
      name: `تحليل زمني لـ ${amountColumns[0].name}`,
      description: `استعلام لتحليل ${amountColumns[0].name} عبر الزمن`,
      example_sql: `SELECT YEAR(${dateColumns[0].name}) as Year, MONTH(${dateColumns[0].name}) as Month, SUM(${amountColumns[0].name}) as Total FROM ${table.name} GROUP BY YEAR(${dateColumns[0].name}), MONTH(${dateColumns[0].name}) ORDER BY Year, Month`
    });
  }

  return useCases;
}
