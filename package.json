{"name": "sql-ai-agent", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test:db": "node scripts/test-connection.js", "setup": "npm install && npm run test:db", "clean": "rm -rf .next node_modules/.cache", "type-check": "tsc --noEmit"}, "dependencies": {"react": "19.1.0", "react-dom": "19.1.0", "next": "15.4.3", "mssql": "^11.0.1", "langchain": "^0.3.7", "openai": "^4.73.1", "recharts": "^2.15.0", "lucide-react": "^0.468.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "tailwind-merge": "^2.5.5", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-slot": "^1.1.1"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.4.3", "@eslint/eslintrc": "^3", "@types/mssql": "^9.1.5"}}