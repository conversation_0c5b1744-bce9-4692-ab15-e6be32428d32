import fs from 'fs/promises';
import path from 'path';
import crypto from 'crypto';

interface DatabaseConfig {
  server: string;
  database: string;
  username?: string;
  port: string;
}

interface CachedDatabase {
  id: string;
  config: DatabaseConfig;
  schema: any;
  indexed_at: string;
  last_accessed: string;
  version: string;
}

interface DatabaseCache {
  databases: CachedDatabase[];
  version: string;
}

const CACHE_FILE_PATH = path.join(process.cwd(), 'src/data/database-cache.json');
const CACHE_VERSION = '1.0.0';

// إنشاء معرف فريد لقاعدة البيانات
export function generateDatabaseId(config: DatabaseConfig): string {
  const configString = `${config.server}:${config.port}:${config.database}:${config.username || 'default'}`;
  return crypto.createHash('md5').update(configString).digest('hex');
}

// تحميل ذاكرة التخزين المؤقت
export async function loadDatabaseCache(): Promise<DatabaseCache> {
  try {
    const cacheData = await fs.readFile(CACHE_FILE_PATH, 'utf-8');
    const cache = JSON.parse(cacheData) as DatabaseCache;
    
    // التحقق من إصدار ذاكرة التخزين المؤقت
    if (cache.version !== CACHE_VERSION) {
      console.log('إصدار ذاكرة التخزين المؤقت قديم، سيتم إنشاء ذاكرة جديدة');
      return createEmptyCache();
    }
    
    return cache;
  } catch (error) {
    console.log('لم يتم العثور على ذاكرة التخزين المؤقت، سيتم إنشاء ذاكرة جديدة');
    return createEmptyCache();
  }
}

// إنشاء ذاكرة تخزين مؤقت فارغة
function createEmptyCache(): DatabaseCache {
  return {
    databases: [],
    version: CACHE_VERSION
  };
}

// حفظ ذاكرة التخزين المؤقت
export async function saveDatabaseCache(cache: DatabaseCache): Promise<void> {
  try {
    // إنشاء مجلد البيانات إذا لم يكن موجوداً
    const dataDir = path.dirname(CACHE_FILE_PATH);
    try {
      await fs.access(dataDir);
    } catch {
      await fs.mkdir(dataDir, { recursive: true });
    }
    
    await fs.writeFile(CACHE_FILE_PATH, JSON.stringify(cache, null, 2), 'utf-8');
  } catch (error) {
    console.error('خطأ في حفظ ذاكرة التخزين المؤقت:', error);
    throw error;
  }
}

// البحث عن قاعدة بيانات في ذاكرة التخزين المؤقت
export async function findCachedDatabase(config: DatabaseConfig): Promise<CachedDatabase | null> {
  const cache = await loadDatabaseCache();
  const databaseId = generateDatabaseId(config);
  
  const cachedDb = cache.databases.find(db => db.id === databaseId);
  
  if (cachedDb) {
    // تحديث وقت آخر وصول
    cachedDb.last_accessed = new Date().toISOString();
    await saveDatabaseCache(cache);
  }
  
  return cachedDb || null;
}

// إضافة قاعدة بيانات إلى ذاكرة التخزين المؤقت
export async function addDatabaseToCache(config: DatabaseConfig, schema: any): Promise<void> {
  const cache = await loadDatabaseCache();
  const databaseId = generateDatabaseId(config);
  
  // إزالة قاعدة البيانات إذا كانت موجودة مسبقاً
  cache.databases = cache.databases.filter(db => db.id !== databaseId);
  
  // إضافة قاعدة البيانات الجديدة
  const newDatabase: CachedDatabase = {
    id: databaseId,
    config: {
      server: config.server,
      database: config.database,
      username: config.username,
      port: config.port
    },
    schema,
    indexed_at: new Date().toISOString(),
    last_accessed: new Date().toISOString(),
    version: CACHE_VERSION
  };
  
  cache.databases.push(newDatabase);
  
  // الاحتفاظ بآخر 10 قواعد بيانات فقط
  if (cache.databases.length > 10) {
    cache.databases.sort((a, b) => 
      new Date(b.last_accessed).getTime() - new Date(a.last_accessed).getTime()
    );
    cache.databases = cache.databases.slice(0, 10);
  }
  
  await saveDatabaseCache(cache);
}

// إزالة قاعدة بيانات من ذاكرة التخزين المؤقت
export async function removeDatabaseFromCache(config: DatabaseConfig): Promise<void> {
  const cache = await loadDatabaseCache();
  const databaseId = generateDatabaseId(config);
  
  cache.databases = cache.databases.filter(db => db.id !== databaseId);
  await saveDatabaseCache(cache);
}

// الحصول على قائمة قواعد البيانات المحفوظة
export async function getCachedDatabases(): Promise<CachedDatabase[]> {
  const cache = await loadDatabaseCache();
  return cache.databases.sort((a, b) => 
    new Date(b.last_accessed).getTime() - new Date(a.last_accessed).getTime()
  );
}

// تنظيف ذاكرة التخزين المؤقت (إزالة القواعد القديمة)
export async function cleanupCache(maxAge: number = 30): Promise<void> {
  const cache = await loadDatabaseCache();
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - maxAge);
  
  const initialCount = cache.databases.length;
  cache.databases = cache.databases.filter(db => 
    new Date(db.last_accessed) > cutoffDate
  );
  
  const removedCount = initialCount - cache.databases.length;
  if (removedCount > 0) {
    console.log(`تم إزالة ${removedCount} قاعدة بيانات قديمة من ذاكرة التخزين المؤقت`);
    await saveDatabaseCache(cache);
  }
}

// التحقق من صحة ذاكرة التخزين المؤقت
export async function validateCache(): Promise<boolean> {
  try {
    const cache = await loadDatabaseCache();
    
    // التحقق من هيكل البيانات
    if (!cache.databases || !Array.isArray(cache.databases)) {
      return false;
    }
    
    // التحقق من كل قاعدة بيانات
    for (const db of cache.databases) {
      if (!db.id || !db.config || !db.schema || !db.indexed_at) {
        return false;
      }
    }
    
    return true;
  } catch (error) {
    return false;
  }
}

// إعادة تعيين ذاكرة التخزين المؤقت
export async function resetCache(): Promise<void> {
  const emptyCache = createEmptyCache();
  await saveDatabaseCache(emptyCache);
}

// الحصول على إحصائيات ذاكرة التخزين المؤقت
export async function getCacheStats(): Promise<{
  totalDatabases: number;
  oldestAccess: string | null;
  newestAccess: string | null;
  totalSize: number;
}> {
  const cache = await loadDatabaseCache();
  
  if (cache.databases.length === 0) {
    return {
      totalDatabases: 0,
      oldestAccess: null,
      newestAccess: null,
      totalSize: 0
    };
  }
  
  const accessTimes = cache.databases.map(db => new Date(db.last_accessed));
  const oldestAccess = new Date(Math.min(...accessTimes.map(d => d.getTime())));
  const newestAccess = new Date(Math.max(...accessTimes.map(d => d.getTime())));
  
  // حساب حجم ذاكرة التخزين المؤقت (تقريبي)
  const cacheString = JSON.stringify(cache);
  const totalSize = Buffer.byteLength(cacheString, 'utf8');
  
  return {
    totalDatabases: cache.databases.length,
    oldestAccess: oldestAccess.toISOString(),
    newestAccess: newestAccess.toISOString(),
    totalSize
  };
}
