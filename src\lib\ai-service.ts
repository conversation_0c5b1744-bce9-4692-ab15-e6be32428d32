import OpenAI from 'openai';
import fs from 'fs/promises';
import path from 'path';

// Helper function to extract and parse JSON from AI responses
function extractAndParseJSON(content: string): any {
  console.log('🔍 محاولة تحليل الاستجابة:', content.substring(0, 200) + '...');

  try {
    // First try to parse directly
    const result = JSON.parse(content);
    console.log('✅ تم تحليل JSON مباشرة');
    return result;
  } catch (directError) {
    console.log('❌ فشل التحليل المباشر:', directError instanceof Error ? directError.message : String(directError));

    // If direct parsing fails, try to extract JSO<PERSON> from markdown blocks
    const jsonMatch = content.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/);
    if (jsonMatch) {
      try {
        const result = JSON.parse(jsonMatch[1]);
        console.log('✅ تم استخراج JSON من markdown');
        return result;
      } catch (markdownError) {
        console.log('❌ فشل تحليل JSON من markdown:', markdownError instanceof Error ? markdownError.message : String(markdownError));
      }
    }

    // Try to find JSON-like content without markdown
    const jsonStart = content.indexOf('{');
    const jsonEnd = content.lastIndexOf('}');
    if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
      try {
        const jsonContent = content.substring(jsonStart, jsonEnd + 1);
        const result = JSON.parse(jsonContent);
        console.log('✅ تم استخراج JSON من النص');
        return result;
      } catch (extractError) {
        console.log('❌ فشل استخراج JSON من النص:', extractError instanceof Error ? extractError.message : String(extractError));
      }
    }

    console.error('❌ فشل في جميع محاولات تحليل JSON. المحتوى:', content);
    throw new Error(`Could not extract valid JSON from response: ${content.substring(0, 200)}...`);
  }
}

// إعداد OpenRouter API
const openai = new OpenAI({
  apiKey: process.env.OPENROUTER_API_KEY || '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',
  baseURL: 'https://openrouter.ai/api/v1',
});

const MODEL_NAME = 'qwen/qwen-2.5-72b-instruct';

interface QueryAnalysis {
  success: boolean;
  intent: string;
  entities: Record<string, any>;
  tables: string[];
  columns: string[];
  visualization: string;
  confidence: number;
}

interface SQLGeneration {
  success: boolean;
  sql: string;
  explanation: string;
}

interface ResultAnalysis {
  success: boolean;
  insights: string;
  patterns: string[];
  recommendations: string[];
}

// تحميل ملفات النيات والكيانات
async function loadIntentsAndEntities() {
  try {
    const intentsPath = path.join(process.cwd(), 'src/data/intents.json');
    const entitiesPath = path.join(process.cwd(), 'src/data/entities.json');
    const useCasesPath = path.join(process.cwd(), 'src/data/use-cases.json');
    
    const [intentsData, entitiesData, useCasesData] = await Promise.all([
      fs.readFile(intentsPath, 'utf-8'),
      fs.readFile(entitiesPath, 'utf-8'),
      fs.readFile(useCasesPath, 'utf-8')
    ]);

    return {
      intents: JSON.parse(intentsData),
      entities: JSON.parse(entitiesData),
      useCases: JSON.parse(useCasesData)
    };
  } catch (error) {
    console.error('خطأ في تحميل ملفات النيات والكيانات:', error);
    return null;
  }
}

// تحميل مخطط قاعدة البيانات
async function loadDatabaseSchema() {
  try {
    const schemaPath = path.join(process.cwd(), 'src/data/database-schema.json');
    const schemaData = await fs.readFile(schemaPath, 'utf-8');
    return JSON.parse(schemaData);
  } catch (error) {
    console.error('خطأ في تحميل مخطط قاعدة البيانات:', error);
    return null;
  }
}

export async function analyzeQuery(query: string): Promise<QueryAnalysis> {
  try {
    const data = await loadIntentsAndEntities();
    const schema = await loadDatabaseSchema();
    
    if (!data || !schema) {
      return {
        success: false,
        intent: '',
        entities: {},
        tables: [],
        columns: [],
        visualization: 'table',
        confidence: 0
      };
    }

    const prompt = `
أنت محلل ذكي للاستعلامات باللغة العربية متخصص في تحليل البيانات التجارية. مهمتك تحليل الاستعلام التالي واستخراج النية والكيانات بدقة عالية.

الاستعلام: "${query}"

النيات المتاحة:
${JSON.stringify(data.intents.intents, null, 2)}

الكيانات المتاحة مع التصنيف الذكي:
${JSON.stringify(data.entities, null, 2)}

مخطط قاعدة البيانات مع التصنيف الذكي:
${JSON.stringify(schema, null, 2)}

قواعد التحليل المتقدم:
1. حدد الفئة الرئيسية للاستعلام (المنتجات، العملاء، المخازن، المبالغ، التواريخ، الفواتير، المحاسبة)
2. استخدم التصنيف الذكي للأعمدة لاختيار الأعمدة الأنسب
3. تعرف على الأرقام والكميات والفترات الزمنية بدقة
4. ميز بين أنواع التحليل المختلفة (ترتيب، مقارنة، اتجاهات، إحصائيات)
5. اختر نوع التصور الأنسب حسب طبيعة البيانات

يرجى تحليل الاستعلام وإرجاع النتيجة بصيغة JSON فقط بدون أي نص إضافي أو تنسيق markdown بالتنسيق التالي:
{
  "success": true,
  "intent": "اسم النية المناسبة",
  "category": "الفئة الرئيسية للاستعلام",
  "entities": {
    "entity_name": "القيمة المستخرجة"
  },
  "tables": ["أسماء الجداول المطلوبة"],
  "columns": ["أسماء الأعمدة المطلوبة مع أسماء الجداول"],
  "filters": {
    "date_range": "الفترة الزمنية إن وجدت",
    "conditions": ["الشروط الإضافية"]
  },
  "visualization": "نوع التصور المناسب (table, bar, line, pie)",
  "confidence": 0.95,
  "analysis_type": "نوع التحليل (ranking, comparison, trend, summary)"
}

أرجع JSON فقط بدون أي نص آخر.
`;

    const response = await openai.chat.completions.create({
      model: MODEL_NAME,
      messages: [
        {
          role: 'system',
          content: 'أنت محلل ذكي للاستعلامات باللغة العربية. تحلل الاستعلامات وتستخرج النيات والكيانات بدقة.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.1,
      max_tokens: 1000
    });

    const result = response.choices[0]?.message?.content;
    if (!result) {
      throw new Error('لم يتم الحصول على رد من النموذج اللغوي');
    }

    // محاولة تحليل الرد كـ JSON
    try {
      const analysis = extractAndParseJSON(result);
      return analysis;
    } catch (parseError) {
      console.error('خطأ في تحليل رد النموذج اللغوي:', parseError);
      return {
        success: false,
        intent: '',
        entities: {},
        tables: [],
        columns: [],
        visualization: 'table',
        confidence: 0
      };
    }

  } catch (error) {
    console.error('خطأ في تحليل الاستعلام:', error);
    return {
      success: false,
      intent: '',
      entities: {},
      tables: [],
      columns: [],
      visualization: 'table',
      confidence: 0
    };
  }
}

export async function generateSQL(queryAnalysis: QueryAnalysis): Promise<SQLGeneration> {
  try {
    const schema = await loadDatabaseSchema();
    const useCases = await loadIntentsAndEntities();
    
    if (!schema || !useCases) {
      return {
        success: false,
        sql: '',
        explanation: 'فشل في تحميل البيانات المطلوبة'
      };
    }

    const prompt = `
أنت خبير في كتابة استعلامات SQL Server متخصص في التحليلات التجارية. مهمتك إنشاء استعلام SQL دقيق وفعال ومحسن للأداء.

تحليل الاستعلام المتقدم:
${JSON.stringify(queryAnalysis, null, 2)}

مخطط قاعدة البيانات مع التصنيف الذكي:
${JSON.stringify(schema, null, 2)}

أمثلة حالات الاستخدام المتقدمة:
${JSON.stringify(useCases?.useCases, null, 2)}

قواعد إنشاء SQL المتقدم:
1. استخدم التصنيف الذكي لاختيار الأعمدة الأنسب
2. طبق الفلاتر الزمنية بدقة (هذا الشهر، الشهر الماضي، الربع، السنة)
3. استخدم الشروط المناسبة للمستندات (فاتورة مبيعات، فاتورة مشتريات)
4. أضف التجميع والترتيب المناسب حسب نوع التحليل
5. استخدم الدوال المناسبة (SUM, COUNT, AVG, MAX, MIN)
6. أضف DISTINCT عند الحاجة لتجنب التكرار
7. استخدم JOIN إذا كان هناك أكثر من جدول

أمثلة للفلاتر الزمنية:
- هذا الشهر: WHERE YEAR(TheDate) = YEAR(GETDATE()) AND MONTH(TheDate) = MONTH(GETDATE())
- الشهر الماضي: WHERE YEAR(TheDate) = YEAR(DATEADD(MONTH, -1, GETDATE())) AND MONTH(TheDate) = MONTH(DATEADD(MONTH, -1, GETDATE()))
- هذا العام: WHERE YEAR(TheDate) = YEAR(GETDATE())
- الربع الحالي: WHERE YEAR(TheDate) = YEAR(GETDATE()) AND DATEPART(QUARTER, TheDate) = DATEPART(QUARTER, GETDATE())

يرجى إنشاء استعلام SQL مناسب وإرجاع النتيجة بالتنسيق التالي:
{
  "success": true,
  "sql": "استعلام SQL كامل وصحيح ومحسن",
  "explanation": "شرح مفصل للاستعلام باللغة العربية",
  "performance_tips": ["نصائح لتحسين الأداء"],
  "alternative_queries": ["استعلامات بديلة إن وجدت"]
}

ملاحظات مهمة:
- استخدم أسماء الجداول والأعمدة الصحيحة من المخطط
- تأكد من صحة صيغة SQL Server
- استخدم الفلاتر والشروط المناسبة حسب السياق
- أضف ORDER BY و TOP/LIMIT إذا كان مناسباً
- استخدم GROUP BY للتجميع عند الحاجة
- تجنب SELECT * واستخدم أسماء الأعمدة المحددة
- استخدم الفهارس المناسبة للأداء الأمثل
`;

    const response = await openai.chat.completions.create({
      model: MODEL_NAME,
      messages: [
        {
          role: 'system',
          content: 'أنت خبير في SQL Server وتكتب استعلامات دقيقة وفعالة باللغة العربية.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.1,
      max_tokens: 1500
    });

    const result = response.choices[0]?.message?.content;
    if (!result) {
      throw new Error('لم يتم الحصول على رد من النموذج اللغوي');
    }

    try {
      const sqlGeneration = extractAndParseJSON(result);
      return sqlGeneration;
    } catch (parseError) {
      console.error('خطأ في تحليل رد النموذج اللغوي:', parseError);
      return {
        success: false,
        sql: '',
        explanation: 'فشل في تحليل رد النموذج اللغوي'
      };
    }

  } catch (error) {
    console.error('خطأ في توليد SQL:', error);
    return {
      success: false,
      sql: '',
      explanation: 'فشل في توليد استعلام SQL'
    };
  }
}

// دالة لتحسين الاستعلامات باستخدام التصنيف الذكي
export async function enhanceQueryWithIntelligentClassification(query: string, schema: any): Promise<any> {
  try {
    // استخدام التصنيف الذكي لتحسين فهم الاستعلام
    if (schema.intelligentClassification) {
      const classification = schema.intelligentClassification;

      // تحديد الفئة الأنسب للاستعلام
      let detectedCategory = null;
      let relevantColumns = [];

      // تحليل الكلمات المفتاحية في الاستعلام
      const queryLower = query.toLowerCase();

      if (queryLower.includes('منتج') || queryLower.includes('صنف') || queryLower.includes('سلعة')) {
        detectedCategory = 'المنتجات';
        relevantColumns = classification.المنتجات.columns;
      } else if (queryLower.includes('عميل') || queryLower.includes('زبون') || queryLower.includes('موزع')) {
        detectedCategory = 'العملاء';
        relevantColumns = classification.العملاء.columns;
      } else if (queryLower.includes('فرع') || queryLower.includes('مخزن') || queryLower.includes('مستودع')) {
        detectedCategory = 'المخازن';
        relevantColumns = classification.المخازن.columns;
      } else if (queryLower.includes('مبلغ') || queryLower.includes('سعر') || queryLower.includes('مبيعات')) {
        detectedCategory = 'المبالغ';
        relevantColumns = classification.المبالغ.columns;
      } else if (queryLower.includes('تاريخ') || queryLower.includes('شهر') || queryLower.includes('سنة')) {
        detectedCategory = 'التواريخ';
        relevantColumns = classification.التواريخ.columns;
      } else if (queryLower.includes('فاتورة') || queryLower.includes('مستند')) {
        detectedCategory = 'الفواتير';
        relevantColumns = classification.الفواتير.columns;
      }

      return {
        detectedCategory,
        relevantColumns,
        enhancedQuery: query,
        suggestions: generateQuerySuggestions(detectedCategory, relevantColumns)
      };
    }

    return { detectedCategory: null, relevantColumns: [], enhancedQuery: query, suggestions: [] };
  } catch (error) {
    console.error('خطأ في تحسين الاستعلام:', error);
    return { detectedCategory: null, relevantColumns: [], enhancedQuery: query, suggestions: [] };
  }
}

// دالة لتوليد اقتراحات الاستعلامات
function generateQuerySuggestions(category: string | null, columns: string[]): string[] {
  if (!category || columns.length === 0) return [];

  const suggestions = [];

  switch (category) {
    case 'المنتجات':
      suggestions.push('أعرض لي أكثر 10 منتجات مبيعاً');
      suggestions.push('ما هي المنتجات الأكثر ربحية؟');
      suggestions.push('تحليل مبيعات المنتجات حسب الفئة');
      break;
    case 'العملاء':
      suggestions.push('أكثر العملاء شراءً هذا الشهر');
      suggestions.push('تحليل سلوك العملاء');
      suggestions.push('العملاء الجدد مقابل العملاء المتكررين');
      break;
    case 'المخازن':
      suggestions.push('أداء الفروع المختلفة');
      suggestions.push('مقارنة مبيعات الفروع');
      suggestions.push('تحليل المخزون حسب الفرع');
      break;
    case 'المبالغ':
      suggestions.push('إجمالي المبيعات هذا الشهر');
      suggestions.push('تحليل الأرباح والخسائر');
      suggestions.push('مقارنة الإيرادات بالفترات السابقة');
      break;
    case 'التواريخ':
      suggestions.push('اتجاهات المبيعات الشهرية');
      suggestions.push('مقارنة الأداء السنوي');
      suggestions.push('تحليل الموسمية');
      break;
    case 'الفواتير':
      suggestions.push('تحليل أنواع الفواتير');
      suggestions.push('إحصائيات المستندات');
      suggestions.push('تتبع الفواتير المعلقة');
      break;
  }

  return suggestions;
}

export async function analyzeResults(originalQuery: string, data: any[], queryAnalysis: QueryAnalysis): Promise<ResultAnalysis> {
  try {
    const prompt = `
أنت محلل بيانات خبير ومستشار أعمال متخصص في التحليلات التجارية. مهمتك تحليل نتائج الاستعلام وتقديم رؤى ذكية وتوصيات عملية.

الاستعلام الأصلي: "${originalQuery}"

تحليل الاستعلام المتقدم:
${JSON.stringify(queryAnalysis, null, 2)}

البيانات (أول 10 صفوف):
${JSON.stringify(data.slice(0, 10), null, 2)}

إجمالي عدد الصفوف: ${data.length}

إحصائيات إضافية:
- عدد الأعمدة: ${data.length > 0 ? Object.keys(data[0]).length : 0}
- أسماء الأعمدة: ${data.length > 0 ? Object.keys(data[0]).join(', ') : 'لا توجد بيانات'}

قواعد التحليل المتقدم:
1. حلل البيانات حسب الفئة (منتجات، عملاء، مبيعات، إلخ)
2. اكتشف الأنماط والاتجاهات المهمة
3. قدم مقارنات ذكية بين القيم
4. احسب النسب والمعدلات المهمة
5. قدم توصيات عملية قابلة للتنفيذ
6. اربط النتائج بالسياق التجاري
7. حدد الفرص والتحديات

يرجى تحليل البيانات وإرجاع النتيجة بالتنسيق التالي:
{
  "success": true,
  "insights": "تحليل مفصل وذكي للنتائج باللغة العربية مع السياق التجاري",
  "key_findings": ["النتيجة الرئيسية 1", "النتيجة الرئيسية 2", "النتيجة الرئيسية 3"],
  "patterns": ["نمط مهم 1", "نمط مهم 2", "نمط مهم 3"],
  "statistics": {
    "total_records": ${data.length},
    "top_value": "أعلى قيمة مع التفسير",
    "average": "المتوسط مع التفسير",
    "trends": "الاتجاهات المكتشفة"
  },
  "recommendations": [
    {
      "title": "توصية 1",
      "description": "وصف مفصل",
      "priority": "عالية/متوسطة/منخفضة",
      "impact": "التأثير المتوقع"
    }
  ],
  "business_insights": ["رؤية تجارية 1", "رؤية تجارية 2"],
  "next_steps": ["خطوة تالية 1", "خطوة تالية 2"]
}

يجب أن يتضمن التحليل:
- ملخص تنفيذي للنتائج الرئيسية
- الأنماط والاتجاهات المكتشفة مع التفسير
- المقارنات والإحصائيات المهمة
- التوصيات العملية مع الأولوية
- رؤى تجارية قابلة للتنفيذ
- تحليل الفرص والمخاطر
- خطوات تالية مقترحة
`;

    const response = await openai.chat.completions.create({
      model: MODEL_NAME,
      messages: [
        {
          role: 'system',
          content: 'أنت محلل بيانات خبير تقدم رؤى ذكية وتوصيات عملية باللغة العربية.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.3,
      max_tokens: 2000
    });

    const result = response.choices[0]?.message?.content;
    if (!result) {
      throw new Error('لم يتم الحصول على رد من النموذج اللغوي');
    }

    try {
      const analysis = extractAndParseJSON(result);
      return analysis;
    } catch (parseError) {
      console.error('خطأ في تحليل رد النموذج اللغوي:', parseError);
      return {
        success: true,
        insights: result, // استخدام النص كما هو إذا فشل التحليل
        patterns: [],
        recommendations: []
      };
    }

  } catch (error) {
    console.error('خطأ في تحليل النتائج:', error);
    return {
      success: false,
      insights: 'فشل في تحليل النتائج',
      patterns: [],
      recommendations: []
    };
  }
}
