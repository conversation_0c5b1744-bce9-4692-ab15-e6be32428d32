'use client';

import { useState, useEffect } from 'react';
import { Bar<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, TrendingUp, Download, Maximize2 } from 'lucide-react';

interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string | string[];
    borderColor?: string;
    borderWidth?: number;
  }[];
}

interface AdvancedChartsProps {
  data: any[];
  visualization: 'bar' | 'line' | 'pie' | 'table';
  title: string;
  query: string;
}

export default function AdvancedCharts({ data, visualization, title, query }: AdvancedChartsProps) {
  const [chartData, setChartData] = useState<ChartData | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);

  useEffect(() => {
    if (data && data.length > 0) {
      generateChartData();
    }
  }, [data, visualization]);

  const generateChartData = () => {
    if (!data || data.length === 0) return;

    const firstRow = data[0];
    const columns = Object.keys(firstRow);
    
    // تحديد الأعمدة النصية والرقمية
    const textColumns = columns.filter(col => 
      typeof firstRow[col] === 'string' && isNaN(Number(firstRow[col]))
    );
    const numericColumns = columns.filter(col => 
      typeof firstRow[col] === 'number' || !isNaN(Number(firstRow[col]))
    );

    if (textColumns.length === 0 || numericColumns.length === 0) return;

    const labelColumn = textColumns[0];
    const valueColumn = numericColumns[0];

    const labels = data.map(row => row[labelColumn] || 'غير محدد');
    const values = data.map(row => Number(row[valueColumn]) || 0);

    // ألوان متدرجة للرسوم البيانية
    const colors = [
      '#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6',
      '#06B6D4', '#84CC16', '#F97316', '#EC4899', '#6366F1'
    ];

    const chartData: ChartData = {
      labels,
      datasets: [{
        label: valueColumn,
        data: values,
        backgroundColor: visualization === 'pie' 
          ? colors.slice(0, labels.length)
          : colors[0] + '80', // شفافية 50%
        borderColor: colors[0],
        borderWidth: 2
      }]
    };

    setChartData(chartData);
  };

  const exportChart = () => {
    // تصدير البيانات كـ CSV
    if (!data || data.length === 0) return;

    const headers = Object.keys(data[0]);
    const csvContent = [
      headers.join(','),
      ...data.map(row => headers.map(header => row[header]).join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `chart_data_${Date.now()}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  if (!data || data.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="text-center py-8">
          <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500 dark:text-gray-400">لا توجد بيانات لعرضها</p>
        </div>
      </div>
    );
  }

  const ChartIcon = visualization === 'bar' ? BarChart3 : 
                   visualization === 'line' ? LineChart : 
                   visualization === 'pie' ? PieChart : TrendingUp;

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow ${isFullscreen ? 'fixed inset-4 z-50' : ''}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <ChartIcon className="w-6 h-6 text-blue-600 dark:text-blue-400 mr-3" />
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              {title}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {data.length} صف من البيانات
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2 space-x-reverse">
          <button
            onClick={exportChart}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
            title="تصدير البيانات"
          >
            <Download className="w-5 h-5" />
          </button>
          <button
            onClick={toggleFullscreen}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
            title={isFullscreen ? "تصغير" : "ملء الشاشة"}
          >
            <Maximize2 className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Chart Content */}
      <div className="p-6">
        {visualization === 'table' ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  {Object.keys(data[0]).map((header) => (
                    <th
                      key={header}
                      className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                    >
                      {header}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {data.slice(0, 50).map((row, index) => (
                  <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    {Object.values(row).map((value: any, cellIndex) => (
                      <td
                        key={cellIndex}
                        className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100"
                      >
                        {typeof value === 'number' ? value.toLocaleString('ar-SA') : value}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
            {data.length > 50 && (
              <div className="mt-4 text-center text-sm text-gray-500 dark:text-gray-400">
                عرض أول 50 صف من أصل {data.length} صف
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {/* Chart Placeholder */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-8 text-center">
              <ChartIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h4 className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">
                رسم بياني {visualization === 'bar' ? 'أعمدة' : 
                           visualization === 'line' ? 'خطي' : 
                           visualization === 'pie' ? 'دائري' : 'متقدم'}
              </h4>
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                سيتم عرض الرسم البياني هنا عند تكامل مكتبة الرسوم البيانية
              </p>
              
              {/* Data Summary */}
              {chartData && (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
                  <div className="bg-white dark:bg-gray-800 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                      {chartData.datasets[0].data.length}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">عدد النقاط</div>
                  </div>
                  <div className="bg-white dark:bg-gray-800 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                      {Math.max(...chartData.datasets[0].data).toLocaleString('ar-SA')}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">أعلى قيمة</div>
                  </div>
                  <div className="bg-white dark:bg-gray-800 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                      {Math.min(...chartData.datasets[0].data).toLocaleString('ar-SA')}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">أقل قيمة</div>
                  </div>
                  <div className="bg-white dark:bg-gray-800 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                      {Math.round(chartData.datasets[0].data.reduce((a, b) => a + b, 0) / chartData.datasets[0].data.length).toLocaleString('ar-SA')}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">المتوسط</div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Query Info */}
      <div className="px-6 py-4 bg-gray-50 dark:bg-gray-700 rounded-b-lg">
        <div className="text-xs text-gray-500 dark:text-gray-400">
          <strong>الاستعلام:</strong> {query}
        </div>
      </div>
    </div>
  );
}
