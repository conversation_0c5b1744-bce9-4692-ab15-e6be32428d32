{"table": "tbltemp_ItemsMain", "description": "هذا الجدول يحتوي على تفاصيل المنتجات الرئيسية، بما في ذلك معلومات حول العملاء، الوثائق، الفروع، والمخازن. يتم استخدامه لتتبع المنتجات والمعاملات المرتبطة بها.", "fields": {"ID": "رقم التسلسلي الفريد لكل سجل في الجدول.", "ParentID": "رقم التسلسلي للمنتج الأصلي إذا كان هذا المنتج فرعيًا.", "DocumentID": "رقم التسلسلي للوثيقة المرتبطة بالمنتج.", "RecordNumber": "رقم السجل في الوثيقة المرتبطة.", "RecordID": "رقم التسلسلي للسجل في الوثيقة المرتبطة.", "TheDate": "تاريخ إدخال المنتج في النظام.", "ClientID": "رقم التسلسلي للعميل المرتبط بالمنتج.", "ClientName": "اسم العميل المرتبط بالمنتج.", "DistributorID": "رقم التسلسلي للمورد المرتبط بالمنتج.", "DistributorName": "اسم المورد المرتبط بالمنتج.", "CurrencyID": "رقم التسلسلي للعملة المستخدمة في المنتج.", "CurrencyName": "اسم العملة المستخدمة في المنتج.", "TheMethodID": "رقم التسلسلي لطريقة الدفع المستخدمة في المنتج.", "TheMethod": "طريقة الدفع المستخدمة في المنتج.", "UserID": "رقم التسلسلي للمستخدم الذي أدخل المنتج في النظام.", "UserName": "اسم المستخدم الذي أدخل المنتج في النظام.", "BranchID": "رقم التسلسلي للفرع الذي تم فيه إجراء المعاملة.", "BranchName": "اسم الفرع الذي تم فيه إجراء المعاملة.", "TheYear": "السنة التي تم فيها إجراء المعاملة.", "DocumentName": "اسم الوثيقة المرتبطة بالمنتج.", "TheNumber": "رقم الوثيقة المرتبطة بالمنتج.", "ItemID": "رقم التسلسلي للمنتج.", "ItemName": "اسم المنتج.", "ItemNumber": "رقم المنتج في النظام.", "CategoryID": "رقم التسلسلي لفئة المنتج.", "CategoryName": "اسم فئة المنتج.", "CategoryNumber": "رقم فئة المنتج.", "ItemTypeID": "رقم التسلسلي لنوع المنتج.", "ItemType": "نوع المنتج.", "ISActive": "حالة المنتج (نشطة أو غير نشطة).", "ISExpiry": "حالة انتهاء صلاحية المنتج.", "ExpiryPoint": "نقطة انتهاء صلاحية المنتج.", "ReorderPoint": "نقطة إعادة الطلب للمنتج.", "UnitID": "رقم التسلسلي للوحدة المستخدمة لقياس المنتج.", "UnitName": "اسم الوحدة المستخدمة لقياس المنتج.", "UnitRank": "ترتيب الوحدة المستخدمة لقياس المنتج.", "ExchangeFactor": "عامل التحويل بين الوحدات المختلفة للمنتج.", "PackageQuantity": "كمية الطرد للمنتج.", "PackageUnitID": "رقم التسلسلي لوحدة الطرد.", "PackageUnitName": "اسم وحدة الطرد.", "Barcode": "الباركود الخاص بالمنتج.", "BarcodeID": "رقم التسلسلي للباركود.", "SerialNumber": "الرقم التسلسلي للمنتج.", "UnitPrice": "سعر الوحدة للمنتج.", "ItemDiscount": "الخصم على المنتج.", "McItemDiscountCurrencyMain": "الخصم على المنتج بالعملة الرئيسية.", "McItemDiscount": "الخصم على المنتج بالعملة المحلية.", "Quantity": "كمية المنتج.", "Bonus": "كمية الهدية أو البونص للمنتج.", "ExpiryDate": "تاريخ انتهاء صلاحية المنتج.", "Amount": "المبلغ الإجمالي للمنتج.", "MCAmount": "المبلغ الإجمالي للمنتج بالعملة الرئيسية.", "MCAmountCurrencyMain": "المبلغ الإجمالي للمنتج بالعملة الرئيسية.", "AccountID": "رقم التسلسلي للحساب المحاسبي.", "AccountName": "اسم الحساب المحاسبي.", "AccountNumber": "رقم الحساب المحاسبي.", "AccountFatherNumber": "رقم الحساب الأب.", "StoreID": "رقم التسلسلي للمخزن الذي تم فيه إجراء المعاملة.", "StoreName": "اسم المخزن الذي تم فيه إجراء المعاملة.", "CostCenterID": "رقم التسلسلي لمركز التكلفة.", "CostCenterName": "اسم مركز التكلفة.", "CostCenterNumber": "رقم مركز التكلفة.", "ExchangePrice": "سعر الصرف بين العملات المختلفة.", "ExchangePriceCurrencyInvetory": "سعر الصرف بين العملات المختلفة بالمخزون.", "NextParentID": "رقم التسلسلي للعنصر التالي الأصلي.", "Notes": "ملاحظات إضافية حول المنتج.", "FatherNumber": "رقم العنصر الأصلي."}, "use_cases": [{"name": "أكثر منتج مبيعاً", "description": "استعلام لعرض أكثر المنتجات مبيعاً حسب الكمية المباعة", "example_sql": "SELECT TOP 10 ItemName, SUM(Quantity) as TotalQuantity FROM tbltemp_ItemsMain WHERE DocumentName = 'فاتورة مبيعات' GROUP BY ItemName ORDER BY TotalQuantity DESC"}, {"name": "أعلى منتج حسب سعر الوحدة", "description": "استعلام لعرض أعلى القيم في المنتجات مرتبة حسب سعر الوحدة", "example_sql": "SELECT TOP 10 ItemName, AVG(UnitPrice) as AveragePrice FROM tbltemp_ItemsMain GROUP BY ItemName ORDER BY AveragePrice DESC"}, {"name": "تحليل زمني للمبيعات", "description": "استعلام لتحليل المبيعات عبر الزمن حسب التاريخ", "example_sql": "SELECT YEAR(TheDate) as Year, MONTH(TheDate) as Month, SUM(Amount) as TotalSales FROM tbltemp_ItemsMain WHERE DocumentName = 'فاتورة مبيعات' GROUP BY YEAR(TheDate), MONTH(TheDate) ORDER BY Year, Month"}, {"name": "أفضل العملاء من حيث الإنفاق", "description": "استعلام لعرض أفضل العملاء من حيث إجمالي الإنفاق", "example_sql": "SELECT TOP 10 ClientName, SUM(Amount) as TotalSpent FROM tbltemp_ItemsMain WHERE DocumentName = 'فاتورة مبيعات' GROUP BY ClientName ORDER BY TotalSpent DESC"}, {"name": "أعلى الفروع مبيعاً", "description": "استعلام لعرض أعلى الفروع من حيث إجمالي المبيعات", "example_sql": "SELECT TOP 10 BranchName, SUM(Amount) as TotalSales FROM tbltemp_ItemsMain WHERE DocumentName = 'فاتورة مبيعات' GROUP BY BranchName ORDER BY TotalSales DESC"}, {"name": "تحليل المنتجات حسب الفئة", "description": "استعلام لتحليل المبيعات حسب فئة المنتج", "example_sql": "SELECT CategoryName, SUM(Quantity) as TotalQuantity, SUM(Amount) as TotalAmount FROM tbltemp_ItemsMain WHERE DocumentName = 'فاتورة مبيعات' GROUP BY CategoryName ORDER BY TotalAmount DESC"}, {"name": "المنتجات منتهية الصلاحية", "description": "استعلام لعرض المنتجات المنتهية الصلاحية أو القريبة من الانتهاء", "example_sql": "SELECT ItemName, ExpiryDate, Quantity FROM tbltemp_ItemsMain WHERE ISExpiry = 1 AND ExpiryDate <= GETDATE() ORDER BY ExpiryDate"}, {"name": "المنتجات ذات المخزون المنخفض", "description": "استقيق لعرض المنتجات التي تقل كمياتها عن نقطة إعادة الطلب", "example_sql": "SELECT ItemName, SUM(Quantity) as CurrentStock, ReorderPoint FROM tbltemp_ItemsMain GROUP BY ItemName, ReorderPoint HAVING SUM(Quantity) <= ReorderPoint ORDER BY CurrentStock"}, {"name": "تحليل الموردين", "description": "استعلام لتحليل الموردين من حيث كمية المشتريات", "example_sql": "SELECT DistributorName, SUM(Quantity) as TotalPurchased FROM tbltemp_ItemsMain WHERE DocumentName = 'فاتورة مشتريات' GROUP BY DistributorName ORDER BY TotalPurchased DESC"}, {"name": "المنتجات الأكثر ربحاً", "description": "استعلام لحساب هامش الربح للمنتجات", "example_sql": "SELECT ItemName, SUM(Amount) as TotalRevenue, SUM(MCAmount) as TotalCost, (SUM(Amount) - SUM(MCAmount)) as Profit FROM tbltemp_ItemsMain GROUP BY ItemName HAVING SUM(Amount) > 0 ORDER BY Profit DESC"}]}