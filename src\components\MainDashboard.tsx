'use client';

import { useState } from 'react';
import { Send, Database, BarChart3, MessageSquare, Settings, TrendingUp } from 'lucide-react';
import QueryInput from './QueryInput';
import ResultsDisplay from './ResultsDisplay';
import AnalysisPanel from './AnalysisPanel';
import CacheManager from './CacheManager';
import SettingsPanel from './SettingsPanel';
import PerformanceMonitor from './PerformanceMonitor';
import { useQuickNotifications } from './NotificationSystem';

interface MainDashboardProps {
  connectionData: any;
}

interface QueryResult {
  id: string;
  query: string;
  sql: string;
  data: any[];
  analysis: string;
  timestamp: Date;
  visualization?: string;
}

export default function MainDashboard({ connectionData }: MainDashboardProps) {
  const [currentQuery, setCurrentQuery] = useState('');
  const [queryHistory, setQueryHistory] = useState<QueryResult[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [activeTab, setActiveTab] = useState<'query' | 'results' | 'analysis' | 'cache'>('query');
  const [showSettings, setShowSettings] = useState(false);
  const [showPerformance, setShowPerformance] = useState(false);
  const notifications = useQuickNotifications();

  const handleQuerySubmit = async (query: string) => {
    if (!query.trim() || isProcessing) return;

    setIsProcessing(true);
    setCurrentQuery(query);

    try {
      // إرسال الاستعلام للمعالجة
      const response = await fetch('/api/query/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query,
          connectionData
        }),
      });

      const result = await response.json();

      if (response.ok) {
        const newResult: QueryResult = {
          id: Date.now().toString(),
          query,
          sql: result.sql,
          data: result.data,
          analysis: result.analysis,
          timestamp: new Date(),
          visualization: result.visualization
        };

        setQueryHistory(prev => [newResult, ...prev]);
        setActiveTab('results');

        // إشعار نجاح
        notifications.success(
          'تم تنفيذ الاستعلام بنجاح',
          `تم العثور على ${result.data.length} صف من البيانات${result.cached ? ' (من التخزين المؤقت)' : ''}`
        );
      } else {
        notifications.error('خطأ في تنفيذ الاستعلام', result.error);
      }
    } catch (error) {
      console.error('خطأ في الشبكة:', error);
      notifications.error('فشل في معالجة الاستعلام', 'تحقق من الاتصال بالإنترنت وحاول مرة أخرى');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Database className="w-8 h-8 text-blue-600 dark:text-blue-400 mr-3" />
              <div>
                <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                  وكيل الذكاء الاصطناعي لـ SQL
                </h1>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  متصل بـ: {connectionData.database}@{connectionData.server}
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setShowSettings(true)}
                className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                title="الإعدادات"
              >
                <Settings className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation Tabs */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-8">
            <button
              onClick={() => setActiveTab('query')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'query'
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <MessageSquare className="w-4 h-4 inline mr-2" />
              الاستعلام
            </button>
            
            <button
              onClick={() => setActiveTab('results')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'results'
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <BarChart3 className="w-4 h-4 inline mr-2" />
              النتائج
              {queryHistory.length > 0 && (
                <span className="mr-2 bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-blue-900 dark:text-blue-300">
                  {queryHistory.length}
                </span>
              )}
            </button>
            
            <button
              onClick={() => setActiveTab('analysis')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'analysis'
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <TrendingUp className="w-4 h-4 inline mr-2" />
              التحليل
            </button>

            <button
              onClick={() => setActiveTab('cache')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'cache'
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <Database className="w-4 h-4 inline mr-2" />
              التخزين المؤقت
            </button>
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'query' && (
          <div className="space-y-6">
            <QueryInput
              onSubmit={handleQuerySubmit}
              isProcessing={isProcessing}
              placeholder="اسأل أي سؤال عن بياناتك... مثل: أعرض لي أكثر 10 منتجات مبيعاً هذا الشهر"
            />
            
            {/* أمثلة الاستعلامات */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                أمثلة على الاستعلامات
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {[
                  'أعرض لي أكثر 5 منتجات مبيعاً',
                  'قارن بين مبيعات هذا الشهر والشهر الماضي',
                  'ما هي أفضل الفروع أداءً؟',
                  'تحليل اتجاهات المبيعات الشهرية',
                  'أكثر العملاء شراءً في الربع الأخير',
                  'تفاصيل المنتج الأكثر ربحية'
                ].map((example, index) => (
                  <button
                    key={index}
                    onClick={() => handleQuerySubmit(example)}
                    disabled={isProcessing}
                    className="text-right p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors text-sm text-gray-700 dark:text-gray-300 disabled:opacity-50"
                  >
                    {example}
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'results' && (
          <ResultsDisplay 
            queryHistory={queryHistory}
            onQuerySelect={(result) => {
              setCurrentQuery(result.query);
              setActiveTab('query');
            }}
          />
        )}

        {activeTab === 'analysis' && (
          <AnalysisPanel
            queryHistory={queryHistory}
            connectionData={connectionData}
          />
        )}

        {activeTab === 'cache' && (
          <CacheManager />
        )}
      </main>

      {/* Settings Panel */}
      <SettingsPanel
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
      />

      {/* Performance Monitor */}
      <PerformanceMonitor
        isVisible={showPerformance}
        onToggle={() => setShowPerformance(!showPerformance)}
      />
    </div>
  );
}
