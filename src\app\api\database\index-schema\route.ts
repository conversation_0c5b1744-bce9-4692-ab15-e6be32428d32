import { NextRequest, NextResponse } from 'next/server';
import sql from 'mssql';
import { findCachedDatabase, addDatabaseToCache } from '@/lib/database-cache';

interface ConnectionData {
  server: string;
  database: string;
  username: string;
  password: string;
  port: string;
  trustServerCertificate: boolean;
}

interface ColumnSchema {
  name: string;
  type: string;
  description: string;
  isPrimaryKey: boolean;
  isForeignKey: boolean;
}

interface TableSchema {
  name: string;
  description: string;
  columns: ColumnSchema[];
}

interface DatabaseSchema {
  tables: TableSchema[];
}

// دالة مساعدة لإرسال تحديثات التقدم
function sendProgressUpdate(step: string, message: string, progress: number) {
  console.log(`📊 [${progress}%] ${step}: ${message}`);
}

export async function POST(request: NextRequest) {
  let pool: sql.ConnectionPool | null = null;
  
  try {
    const connectionData: ConnectionData = await request.json();

    // التحقق من وجود قاعدة البيانات في ذاكرة التخزين المؤقت
    const cachedDatabase = await findCachedDatabase(connectionData);

    if (cachedDatabase) {
      console.log('تم العثور على قاعدة البيانات في ذاكرة التخزين المؤقت');
      return NextResponse.json({
        success: true,
        message: 'تم تحميل قاعدة البيانات من ذاكرة التخزين المؤقت',
        schema: cachedDatabase.schema,
        tablesCount: cachedDatabase.schema.tables.length,
        columnsCount: cachedDatabase.schema.tables.reduce((sum: number, table: TableSchema) => sum + table.columns.length, 0),
        cached: true,
        indexed_at: cachedDatabase.indexed_at
      });
    }

    sendProgressUpdate('بدء العملية', 'بدء الاتصال بقاعدة البيانات...', 5);

    // إعداد تكوين الاتصال
    const config: sql.config = {
      server: connectionData.server,
      database: connectionData.database,
      port: parseInt(connectionData.port) || 1433,
      options: {
        encrypt: true,
        trustServerCertificate: connectionData.trustServerCertificate,
        enableArithAbort: true,
      },
    };

    if (connectionData.username && connectionData.password) {
      config.user = connectionData.username;
      config.password = connectionData.password;
    } else {
      config.authentication = {
        type: 'ntlm',
        options: {
          domain: '',
          userName: '',
          password: '',
        },
      };
    }

    // الاتصال بقاعدة البيانات
    sendProgressUpdate('الاتصال', 'جاري الاتصال بقاعدة البيانات...', 10);
    pool = await sql.connect(config);
    sendProgressUpdate('الاتصال', 'تم الاتصال بقاعدة البيانات بنجاح', 30);

    // استعلام للحصول على معلومات الجداول والأعمدة
    sendProgressUpdate('استخراج البيانات', 'جاري استخراج معلومات الجداول والأعمدة...', 50);
    const tablesQuery = `
      SELECT 
        t.TABLE_NAME,
        c.COLUMN_NAME,
        c.DATA_TYPE,
        c.CHARACTER_MAXIMUM_LENGTH,
        c.IS_NULLABLE,
        c.COLUMN_DEFAULT,
        c.ORDINAL_POSITION,
        CASE WHEN pk.COLUMN_NAME IS NOT NULL THEN 1 ELSE 0 END AS IS_PRIMARY_KEY,
        CASE WHEN fk.COLUMN_NAME IS NOT NULL THEN 1 ELSE 0 END AS IS_FOREIGN_KEY
      FROM INFORMATION_SCHEMA.TABLES t
      LEFT JOIN INFORMATION_SCHEMA.COLUMNS c ON t.TABLE_NAME = c.TABLE_NAME
      LEFT JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE pk ON c.TABLE_NAME = pk.TABLE_NAME 
        AND c.COLUMN_NAME = pk.COLUMN_NAME 
        AND pk.CONSTRAINT_NAME LIKE 'PK_%'
      LEFT JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE fk ON c.TABLE_NAME = fk.TABLE_NAME 
        AND c.COLUMN_NAME = fk.COLUMN_NAME 
        AND fk.CONSTRAINT_NAME LIKE 'FK_%'
      WHERE t.TABLE_TYPE = 'BASE TABLE'
      ORDER BY t.TABLE_NAME, c.ORDINAL_POSITION
    `;

    const result = await pool.request().query(tablesQuery);
    
    // تنظيم البيانات
    const tablesMap = new Map<string, TableSchema>();
    
    for (const row of result.recordset) {
      if (!tablesMap.has(row.TABLE_NAME)) {
        tablesMap.set(row.TABLE_NAME, {
          name: row.TABLE_NAME,
          description: `جدول ${row.TABLE_NAME}`,
          columns: []
        });
      }
      
      const table = tablesMap.get(row.TABLE_NAME)!;
      
      if (row.COLUMN_NAME) {
        const columnType = row.CHARACTER_MAXIMUM_LENGTH 
          ? `${row.DATA_TYPE}(${row.CHARACTER_MAXIMUM_LENGTH})`
          : row.DATA_TYPE;

        table.columns.push({
          name: row.COLUMN_NAME,
          type: columnType,
          description: `عمود ${row.COLUMN_NAME} من نوع ${columnType}`,
          isPrimaryKey: row.IS_PRIMARY_KEY === 1,
          isForeignKey: row.IS_FOREIGN_KEY === 1
        });
      }
    }

    const schema: DatabaseSchema = {
      tables: Array.from(tablesMap.values())
    };

    sendProgressUpdate('إكمال الاستخراج', 'تم استخراج معلومات قاعدة البيانات بنجاح', 90);

    // حفظ قاعدة البيانات في الكاش
    sendProgressUpdate('الحفظ', 'جاري حفظ البيانات في الكاش...', 99);
    await addDatabaseToCache(connectionData, schema);

    await pool.close();

    sendProgressUpdate('مكتملة', 'تم الاتصال بقاعدة البيانات بنجاح!', 100);

    return NextResponse.json({
      success: true,
      message: 'تم الاتصال بقاعدة البيانات بنجاح',
      schema,
      tablesCount: schema.tables.length,
      columnsCount: schema.tables.reduce((sum, table) => sum + table.columns.length, 0),
      cached: false,
      indexed_at: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('خطأ في الاتصال بقاعدة البيانات:', error);
    
    if (pool) {
      try {
        await pool.close();
      } catch (closeError) {
        console.error('خطأ في إغلاق الاتصال:', closeError);
      }
    }

    return NextResponse.json(
      {
        success: false,
        error: 'فشل في الاتصال بقاعدة البيانات',
        details: error.message
      },
      { status: 500 }
    );
  }
}
