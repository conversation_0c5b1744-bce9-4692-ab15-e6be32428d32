#!/usr/bin/env python3
"""
خادم تطوير بسيط لعرض مشروع وكيل الذكاء الاصطناعي لـ SQL
"""

import http.server
import socketserver
import os
import json
import webbrowser
from urllib.parse import urlparse, parse_qs

class SQLAIHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=".", **kwargs)
    
    def do_GET(self):
        """معالجة طلبات GET"""
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/':
            self.serve_demo_page()
        elif parsed_path.path == '/api/demo':
            self.serve_demo_api()
        elif parsed_path.path.startswith('/src/'):
            self.serve_source_file()
        else:
            super().do_GET()
    
    def serve_demo_page(self):
        """عرض صفحة العرض التوضيحي"""
        html_content = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>وكيل الذكاء الاصطناعي لـ SQL - عرض توضيحي</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['Tajawal', 'Arial', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Tajawal', Arial, sans-serif; }
    </style>
</head>
<body class="bg-gray-50 dark:bg-gray-900 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <header class="text-center mb-12">
            <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-8 rounded-lg shadow-lg">
                <h1 class="text-4xl font-bold mb-4">🤖 وكيل الذكاء الاصطناعي لـ SQL</h1>
                <p class="text-xl opacity-90">نظام ذكي متطور للتفاعل مع قواعد البيانات باللغة العربية</p>
            </div>
        </header>

        <!-- Status -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-2xl font-semibold mb-4 text-gray-800 dark:text-white">📊 حالة المشروع</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="bg-green-50 dark:bg-green-900 p-4 rounded-lg border border-green-200 dark:border-green-700">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                        <span class="text-green-800 dark:text-green-200 font-medium">المشروع مكتمل</span>
                    </div>
                </div>
                <div class="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg border border-blue-200 dark:border-blue-700">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-blue-500 rounded-full mr-3"></div>
                        <span class="text-blue-800 dark:text-blue-200 font-medium">جاهز للتشغيل</span>
                    </div>
                </div>
                <div class="bg-purple-50 dark:bg-purple-900 p-4 rounded-lg border border-purple-200 dark:border-purple-700">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-purple-500 rounded-full mr-3"></div>
                        <span class="text-purple-800 dark:text-purple-200 font-medium">ميزات متقدمة</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Features -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <div class="text-3xl mb-4">🧠</div>
                <h3 class="text-xl font-semibold mb-2 text-gray-800 dark:text-white">الذكاء الاصطناعي المتقدم</h3>
                <p class="text-gray-600 dark:text-gray-300">تحويل اللغة العربية الطبيعية إلى استعلامات SQL دقيقة</p>
            </div>
            
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <div class="text-3xl mb-4">📊</div>
                <h3 class="text-xl font-semibold mb-2 text-gray-800 dark:text-white">التصور التفاعلي</h3>
                <p class="text-gray-600 dark:text-gray-300">رسوم بيانية متقدمة وتحليلات ذكية للبيانات</p>
            </div>
            
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <div class="text-3xl mb-4">⚡</div>
                <h3 class="text-xl font-semibold mb-2 text-gray-800 dark:text-white">الأداء المحسن</h3>
                <p class="text-gray-600 dark:text-gray-300">نظام تخزين مؤقت ذكي ومراقبة الأداء</p>
            </div>
            
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <div class="text-3xl mb-4">🔒</div>
                <h3 class="text-xl font-semibold mb-2 text-gray-800 dark:text-white">الأمان المتقدم</h3>
                <p class="text-gray-600 dark:text-gray-300">حماية من SQL Injection ومعالجة آمنة للأخطاء</p>
            </div>
            
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <div class="text-3xl mb-4">🎨</div>
                <h3 class="text-xl font-semibold mb-2 text-gray-800 dark:text-white">واجهة عربية</h3>
                <p class="text-gray-600 dark:text-gray-300">تصميم متجاوب مع دعم كامل للغة العربية (RTL)</p>
            </div>
            
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <div class="text-3xl mb-4">🔧</div>
                <h3 class="text-xl font-semibold mb-2 text-gray-800 dark:text-white">إعدادات متقدمة</h3>
                <p class="text-gray-600 dark:text-gray-300">لوحة إعدادات شاملة مع إمكانية التخصيص</p>
            </div>
        </div>

        <!-- Project Structure -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-2xl font-semibold mb-4 text-gray-800 dark:text-white">📁 هيكل المشروع</h2>
            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg font-mono text-sm">
                <div class="text-gray-700 dark:text-gray-300">
                    src/<br>
                    ├── app/ &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;# صفحات Next.js و API routes<br>
                    ├── components/ &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;# مكونات React قابلة لإعادة الاستخدام<br>
                    ├── lib/ &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;# مكتبات ووظائف مساعدة<br>
                    └── data/ &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;# ملفات البيانات والتكوين
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 class="text-2xl font-semibold mb-4 text-gray-800 dark:text-white">🚀 كيفية التشغيل</h2>
            <div class="space-y-4">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h3 class="font-semibold mb-2 text-gray-800 dark:text-white">1. تثبيت التبعيات</h3>
                    <code class="bg-gray-800 text-green-400 p-2 rounded block">npm install</code>
                </div>
                
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h3 class="font-semibold mb-2 text-gray-800 dark:text-white">2. إعداد متغيرات البيئة</h3>
                    <code class="bg-gray-800 text-green-400 p-2 rounded block">cp .env.local.example .env.local</code>
                </div>
                
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h3 class="font-semibold mb-2 text-gray-800 dark:text-white">3. اختبار الاتصال</h3>
                    <code class="bg-gray-800 text-green-400 p-2 rounded block">npm run test:db</code>
                </div>
                
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h3 class="font-semibold mb-2 text-gray-800 dark:text-white">4. تشغيل التطبيق</h3>
                    <code class="bg-gray-800 text-green-400 p-2 rounded block">npm run dev</code>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <footer class="text-center mt-12 text-gray-600 dark:text-gray-400">
            <p>تم تطوير هذا المشروع بواسطة Augment Agent 🤖</p>
            <p class="mt-2">مشروع مفتوح المصدر - مرخص تحت رخصة MIT</p>
        </footer>
    </div>

    <script>
        // Dark mode toggle
        function toggleDarkMode() {
            document.documentElement.classList.toggle('dark');
        }
        
        // Add dark mode toggle button
        const toggleButton = document.createElement('button');
        toggleButton.innerHTML = '🌙';
        toggleButton.className = 'fixed top-4 left-4 bg-gray-200 dark:bg-gray-700 p-2 rounded-full shadow-lg';
        toggleButton.onclick = toggleDarkMode;
        document.body.appendChild(toggleButton);
    </script>
</body>
</html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html_content.encode('utf-8'))
    
    def serve_demo_api(self):
        """API للعرض التوضيحي"""
        demo_data = {
            "status": "success",
            "message": "مشروع وكيل الذكاء الاصطناعي لـ SQL جاهز للتشغيل",
            "features": [
                "تحويل اللغة العربية إلى SQL",
                "رسوم بيانية تفاعلية",
                "نظام تخزين مؤقت ذكي",
                "إدارة الأخطاء المتقدمة",
                "واجهة عربية متجاوبة"
            ],
            "components": [
                "DatabaseSetup",
                "QueryInput", 
                "ResultsDisplay",
                "AnalysisPanel",
                "CacheManager",
                "SettingsPanel",
                "PerformanceMonitor"
            ]
        }
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.end_headers()
        self.wfile.write(json.dumps(demo_data, ensure_ascii=False).encode('utf-8'))
    
    def serve_source_file(self):
        """عرض ملفات المصدر"""
        try:
            file_path = self.path[1:]  # إزالة الشرطة المائلة الأولى
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                self.send_response(200)
                self.send_header('Content-type', 'text/plain; charset=utf-8')
                self.end_headers()
                self.wfile.write(content.encode('utf-8'))
            else:
                self.send_error(404, "File not found")
        except Exception as e:
            self.send_error(500, f"Error reading file: {str(e)}")

def main():
    PORT = 8000
    
    print("🚀 بدء تشغيل خادم العرض التوضيحي...")
    print(f"📍 الخادم يعمل على: http://localhost:{PORT}")
    print("🔗 سيتم فتح المتصفح تلقائياً...")
    print("⏹️  اضغط Ctrl+C لإيقاف الخادم")
    print("-" * 50)
    
    try:
        with socketserver.TCPServer(("", PORT), SQLAIHandler) as httpd:
            # فتح المتصفح تلقائياً
            webbrowser.open(f'http://localhost:{PORT}')
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")

if __name__ == "__main__":
    main()
