{"product": ["ItemName", "ItemID", "ItemNumber", "ItemType", "ItemTypeID", "Barcode", "BarcodeID", "SerialNumber", "CategoryName", "CategoryID", "CategoryNumber", "ReorderPoint", "ISActive", "ISExpiry", "ExpiryPoint"], "branch": ["BranchName", "BranchID", "<PERSON><PERSON><PERSON><PERSON>"], "client": ["ClientName", "ClientID", "ClientName"], "supplier": ["DistributorName", "DistributorID"], "date": ["TheDate", "EnterTime", "ExpiryDate"], "quantity": ["Quantity", "MainUnitQuantity", "Bonus", "PackageQuantity", "MainUnitBonus"], "amount": ["Amount", "TotalAmount", "MCAmount", "UnitPrice", "TotalAmountByCurrencyInvetory", "MCAmountCurrencyMain", "McItemDiscountCurrencyMain", "Mc<PERSON>temDiscount", "ExchangePrice", "ExchangePriceCurrencyInvetory", "MainUnitPrice"], "store": ["StoreName", "StoreID"], "currency": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "CurrencyID"], "method": ["TheMethod", "TheMethodID"], "document": ["DocumentName", "DocumentID", "TheNumber", "RecordID", "RecordNumber"], "category": ["CategoryName", "CategoryID", "CategoryNumber", "AccountFatherNumber"], "unit": ["UnitName", "UnitID", "UnitRank", "MainUnitID", "PackageUnitName", "PackageUnitID"], "user": ["UserName", "UserID"], "year": ["TheYear"], "notes": ["Notes"], "serial": ["SerialNumber"], "barcode": ["Barcode", "BarcodeID"], "invoice": ["InvoiceID", "TheNumber", "DocumentName", "RecordID"], "cost_center": ["CostCenterName", "CostCenterID", "CostCenterNumber"], "account": ["Account<PERSON><PERSON>", "AccountID", "AccountNumber", "AccountFatherNumber"], "exchange": ["ExchangeFactor", "ExchangePrice", "ExchangePriceCurrencyInvetory"], "discount": ["Discount", "ItemD<PERSON>unt", "Mc<PERSON>temDiscount", "McItemDiscountCurrencyMain"], "bonus": ["Bonus", "MainUnitBonus"], "package": ["PackageQuantity", "PackageUnitName", "PackageUnitID"], "expiry": ["ExpiryDate", "ISExpiry", "ExpiryPoint"], "reorder": ["ReorderPoint", "ExpiryPoint"], "status": ["ISActive", "ISExpiry"], "rank": ["UnitRank"], "factor": ["ExchangeFactor"], "price": ["UnitPrice", "ExchangePrice", "MainUnitPrice", "ExchangePriceCurrencyInvetory"], "main_unit": ["MainUnitQuantity", "MainUnitPrice", "MainUnitID", "MainUnitBonus"], "sub_item": ["NewSubItemEntryID", "NextParentID"], "details": ["DetailsID", "RecordID", "RecordNumber"], "father": ["ParentID", "NextParentID", "AccountFatherNumber", "<PERSON><PERSON><PERSON><PERSON>"], "version": ["RowVersion"], "id": ["ID", "ItemID", "BranchID", "ClientID", "DistributorID", "CurrencyID", "TheMethodID", "UserID", "CategoryID", "UnitID", "AccountID", "StoreID", "BarcodeID", "MainUnitID", "CostCenterID", "PackageUnitID", "InvoiceID", "DetailsID", "NewSubItemEntryID", "ParentID", "NextParentID"], "number": ["ItemNumber", "TheNumber", "CategoryNumber", "CostCenterNumber", "AccountNumber", "<PERSON><PERSON><PERSON><PERSON>", "AccountFatherNumber", "RecordNumber"], "name": ["ItemName", "BranchName", "ClientName", "DistributorName", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TheMethod", "UserName", "CategoryName", "UnitName", "Account<PERSON><PERSON>", "StoreName", "CostCenterName", "PackageUnitName", "DocumentName"], "time": ["EnterTime", "TheDate"], "total": ["TotalAmount", "TotalAmountByCurrencyInvetory"], "mc": ["MCAmount", "MCAmountCurrencyMain", "McItemDiscountCurrencyMain", "Mc<PERSON>temDiscount", "TotalAmountByCurrencyInvetory"], "main": ["MainUnitQuantity", "MainUnitPrice", "MainUnitID", "MainUnitBonus", "MCAmountCurrencyMain", "ExchangePriceCurrencyInvetory"]}