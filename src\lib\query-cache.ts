import fs from 'fs/promises';
import path from 'path';
import crypto from 'crypto';

interface CachedQuery {
  id: string;
  query: string;
  sql: string;
  data: any[];
  analysis: string;
  timestamp: string;
  expiresAt: string;
  executionTime: number;
  dataHash: string;
}

interface QueryCache {
  queries: CachedQuery[];
  version: string;
  lastCleanup: string;
}

const CACHE_FILE_PATH = path.join(process.cwd(), 'src/data/query-cache.json');
const CACHE_VERSION = '1.0.0';
const DEFAULT_TTL = 3600; // 1 hour in seconds
const MAX_CACHE_SIZE = 100;

// إنشاء معرف فريد للاستعلام
export function generateQueryId(query: string, connectionData: any): string {
  const queryString = `${query}:${connectionData.server}:${connectionData.database}`;
  return crypto.createHash('md5').update(queryString).digest('hex');
}

// إنشاء hash للبيانات للتحقق من التغييرات
export function generateDataHash(data: any[]): string {
  const dataString = JSON.stringify(data);
  return crypto.createHash('md5').update(dataString).digest('hex');
}

// تحميل ذاكرة التخزين المؤقت للاستعلامات
export async function loadQueryCache(): Promise<QueryCache> {
  try {
    const cacheData = await fs.readFile(CACHE_FILE_PATH, 'utf-8');
    const cache = JSON.parse(cacheData) as QueryCache;
    
    // التحقق من إصدار ذاكرة التخزين المؤقت
    if (cache.version !== CACHE_VERSION) {
      console.log('إصدار ذاكرة التخزين المؤقت للاستعلامات قديم، سيتم إنشاء ذاكرة جديدة');
      return createEmptyQueryCache();
    }
    
    // تنظيف الاستعلامات المنتهية الصلاحية
    await cleanupExpiredQueries(cache);
    
    return cache;
  } catch (error) {
    console.log('لم يتم العثور على ذاكرة التخزين المؤقت للاستعلامات، سيتم إنشاء ذاكرة جديدة');
    return createEmptyQueryCache();
  }
}

// إنشاء ذاكرة تخزين مؤقت فارغة للاستعلامات
function createEmptyQueryCache(): QueryCache {
  return {
    queries: [],
    version: CACHE_VERSION,
    lastCleanup: new Date().toISOString()
  };
}

// حفظ ذاكرة التخزين المؤقت للاستعلامات
export async function saveQueryCache(cache: QueryCache): Promise<void> {
  try {
    // إنشاء مجلد البيانات إذا لم يكن موجوداً
    const dataDir = path.dirname(CACHE_FILE_PATH);
    try {
      await fs.access(dataDir);
    } catch {
      await fs.mkdir(dataDir, { recursive: true });
    }
    
    await fs.writeFile(CACHE_FILE_PATH, JSON.stringify(cache, null, 2), 'utf-8');
  } catch (error) {
    console.error('خطأ في حفظ ذاكرة التخزين المؤقت للاستعلامات:', error);
    throw error;
  }
}

// البحث عن استعلام في ذاكرة التخزين المؤقت
export async function findCachedQuery(query: string, connectionData: any): Promise<CachedQuery | null> {
  const cache = await loadQueryCache();
  const queryId = generateQueryId(query, connectionData);
  
  const cachedQuery = cache.queries.find(q => q.id === queryId);
  
  if (cachedQuery) {
    // التحقق من انتهاء الصلاحية
    const now = new Date();
    const expiresAt = new Date(cachedQuery.expiresAt);
    
    if (now > expiresAt) {
      // إزالة الاستعلام المنتهي الصلاحية
      cache.queries = cache.queries.filter(q => q.id !== queryId);
      await saveQueryCache(cache);
      return null;
    }
    
    return cachedQuery;
  }
  
  return null;
}

// إضافة استعلام إلى ذاكرة التخزين المؤقت
export async function addQueryToCache(
  query: string, 
  sql: string, 
  data: any[], 
  analysis: string, 
  connectionData: any, 
  executionTime: number,
  ttl: number = DEFAULT_TTL
): Promise<void> {
  const cache = await loadQueryCache();
  const queryId = generateQueryId(query, connectionData);
  const dataHash = generateDataHash(data);
  
  // إزالة الاستعلام إذا كان موجوداً مسبقاً
  cache.queries = cache.queries.filter(q => q.id !== queryId);
  
  // إضافة الاستعلام الجديد
  const now = new Date();
  const expiresAt = new Date(now.getTime() + ttl * 1000);
  
  const newQuery: CachedQuery = {
    id: queryId,
    query,
    sql,
    data,
    analysis,
    timestamp: now.toISOString(),
    expiresAt: expiresAt.toISOString(),
    executionTime,
    dataHash
  };
  
  cache.queries.push(newQuery);
  
  // الاحتفاظ بآخر استعلامات فقط
  if (cache.queries.length > MAX_CACHE_SIZE) {
    cache.queries.sort((a, b) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );
    cache.queries = cache.queries.slice(0, MAX_CACHE_SIZE);
  }
  
  await saveQueryCache(cache);
}

// تنظيف الاستعلامات المنتهية الصلاحية
export async function cleanupExpiredQueries(cache?: QueryCache): Promise<void> {
  if (!cache) {
    cache = await loadQueryCache();
  }
  
  const now = new Date();
  const initialCount = cache.queries.length;
  
  cache.queries = cache.queries.filter(query => {
    const expiresAt = new Date(query.expiresAt);
    return now <= expiresAt;
  });
  
  const removedCount = initialCount - cache.queries.length;
  if (removedCount > 0) {
    console.log(`تم إزالة ${removedCount} استعلام منتهي الصلاحية من ذاكرة التخزين المؤقت`);
    cache.lastCleanup = now.toISOString();
    await saveQueryCache(cache);
  }
}

// الحصول على إحصائيات ذاكرة التخزين المؤقت للاستعلامات
export async function getQueryCacheStats(): Promise<{
  totalQueries: number;
  oldestQuery: string | null;
  newestQuery: string | null;
  totalSize: number;
  hitRate: number;
  averageExecutionTime: number;
}> {
  const cache = await loadQueryCache();
  
  if (cache.queries.length === 0) {
    return {
      totalQueries: 0,
      oldestQuery: null,
      newestQuery: null,
      totalSize: 0,
      hitRate: 0,
      averageExecutionTime: 0
    };
  }
  
  const timestamps = cache.queries.map(q => new Date(q.timestamp));
  const oldestQuery = new Date(Math.min(...timestamps.map(d => d.getTime())));
  const newestQuery = new Date(Math.max(...timestamps.map(d => d.getTime())));
  
  // حساب حجم ذاكرة التخزين المؤقت (تقريبي)
  const cacheString = JSON.stringify(cache);
  const totalSize = Buffer.byteLength(cacheString, 'utf8');
  
  // حساب متوسط وقت التنفيذ
  const averageExecutionTime = cache.queries.reduce((sum, q) => sum + q.executionTime, 0) / cache.queries.length;
  
  return {
    totalQueries: cache.queries.length,
    oldestQuery: oldestQuery.toISOString(),
    newestQuery: newestQuery.toISOString(),
    totalSize,
    hitRate: 0, // يمكن تحسينه لاحقاً بتتبع الـ hits
    averageExecutionTime
  };
}

// إعادة تعيين ذاكرة التخزين المؤقت للاستعلامات
export async function resetQueryCache(): Promise<void> {
  const emptyCache = createEmptyQueryCache();
  await saveQueryCache(emptyCache);
}

// البحث في ذاكرة التخزين المؤقت
export async function searchCachedQueries(searchTerm: string): Promise<CachedQuery[]> {
  const cache = await loadQueryCache();
  const searchTermLower = searchTerm.toLowerCase();
  
  return cache.queries.filter(query => 
    query.query.toLowerCase().includes(searchTermLower) ||
    query.sql.toLowerCase().includes(searchTermLower)
  );
}

// الحصول على الاستعلامات الأكثر استخداماً
export async function getPopularQueries(limit: number = 10): Promise<CachedQuery[]> {
  const cache = await loadQueryCache();
  
  // ترتيب حسب التاريخ (الأحدث أولاً) كبديل لعدد الاستخدامات
  return cache.queries
    .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
    .slice(0, limit);
}
