# ملفات البيانات المساعدة للذكاء الاصطناعي

هذا المجلد يحتوي على الملفات المساعدة التي يتم إنشاؤها تلقائياً بواسطة النظام لمساعدة الذكاء الاصطناعي في فهم قاعدة البيانات والاستعلامات.

## الملفات المُنشأة:

### 1. `intents.json` - ملف النيات
يحتوي على النيات الأساسية للاستعلامات التجارية مثل:
- أكثر المنتجات مبيعاً
- العملاء الأكثر نشاطاً
- مبيعات الفروع
- تحليل المرتجعات

**مثال:**
```json
[
  {
    "intent": "top_selling_products",
    "description": "عرض أكثر المنتجات مبيعًا خلال فترة محددة",
    "keywords": ["أكثر منتج", "المنتجات الأكثر مبيعاً", "top selling"]
  }
]
```

### 2. `entities.json` - ملف الكيانات
يربط الكيانات التجارية بأسماء الأعمدة الفعلية في قاعدة البيانات.

**مثال:**
```json
{
  "product": ["ItemName", "ItemID", "ItemNumber"],
  "branch": ["BranchName", "BranchID"],
  "client": ["ClientName", "ClientID"],
  "quantity": ["Quantity", "Bonus", "MainUnitQuantity"]
}
```

### 3. `synonyms.json` - ملف المرادفات
يساعد في فهم الأسئلة عند استخدام تعابير مختلفة بنفس المعنى.

**مثال:**
```json
{
  "ItemName": ["اسم المنتج", "المنتج", "Product Name", "الصنف"],
  "Quantity": ["الكمية", "عدد", "Quantity", "Qty"],
  "Amount": ["إجمالي", "السعر", "القيمة", "total", "amount"]
}
```

### 4. `use-cases.json` - ملف حالات الاستخدام
يحتوي على سيناريوهات الاستخدام الحقيقية للنظام التجاري.

**مثال:**
```json
[
  {
    "title": "تحديد المنتج الأعلى مبيعاً خلال فترة",
    "user": "مدير المبيعات",
    "exampleQuestions": [
      "ما هو أكثر منتج تم بيعه هذا الأسبوع؟",
      "أريد معرفة أكثر منتج تم بيعه خلال الشهر الحالي."
    ],
    "intent": "top_selling_products",
    "requiredEntities": ["ItemName", "Quantity", "TheDate"],
    "table": "tbltemp_ItemsMain"
  }
]
```

### 5. `database-schema.json` - مخطط قاعدة البيانات
يحتوي على معلومات مفصلة عن جميع الجداول والأعمدة مع أوصافها المحسنة.

## كيف تعمل هذه الملفات معاً؟

1. **تحليل النية**: يستخدم `intents.json` لفهم ما يريده المستخدم
2. **تحديد الكيانات**: يستخدم `entities.json` لربط الكلمات بالأعمدة
3. **فهم المرادفات**: يستخدم `synonyms.json` لفهم التعابير المختلفة
4. **السيناريوهات**: يستخدم `use-cases.json` للتعلم من الأمثلة الواقعية

## التحديث التلقائي

هذه الملفات يتم إنشاؤها وتحديثها تلقائياً عند:
- الاتصال بقاعدة بيانات جديدة
- إعادة فهرسة قاعدة البيانات الحالية
- تغيير في هيكل قاعدة البيانات

⚠️ **تحذير**: لا تقم بتعديل هذه الملفات يدوياً حيث سيتم استبدالها عند إعادة الفهرسة.
