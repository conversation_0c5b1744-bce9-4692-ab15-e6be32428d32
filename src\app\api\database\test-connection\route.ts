import { NextRequest, NextResponse } from 'next/server';
import sql from 'mssql';

interface ConnectionData {
  server: string;
  database: string;
  username: string;
  password: string;
  port: string;
  trustServerCertificate: boolean;
}

export async function POST(request: NextRequest) {
  try {
    const connectionData: ConnectionData = await request.json();

    // إعداد تكوين الاتصال
    const config: sql.config = {
      server: connectionData.server,
      database: connectionData.database,
      port: parseInt(connectionData.port) || 1433,
      options: {
        encrypt: true,
        trustServerCertificate: connectionData.trustServerCertificate,
        enableArithAbort: true,
      },
    };

    // إضافة بيانات المصادقة إذا تم توفيرها
    if (connectionData.username && connectionData.password) {
      config.user = connectionData.username;
      config.password = connectionData.password;
    } else {
      // استخدام Windows Authentication
      config.authentication = {
        type: 'ntlm',
        options: {
          domain: '',
          userName: '',
          password: '',
        },
      };
    }

    // محاولة الاتصال
    const pool = await sql.connect(config);
    
    // اختبار الاتصال بتنفيذ استعلام بسيط
    const result = await pool.request().query('SELECT 1 as test');
    
    // إغلاق الاتصال
    await pool.close();

    return NextResponse.json({
      success: true,
      message: 'تم الاتصال بنجاح',
      serverInfo: {
        server: connectionData.server,
        database: connectionData.database,
        port: connectionData.port
      }
    });

  } catch (error: any) {
    console.error('خطأ في الاتصال بقاعدة البيانات:', error);
    
    let errorMessage = 'فشل في الاتصال بقاعدة البيانات';
    
    if (error.code === 'ELOGIN') {
      errorMessage = 'خطأ في اسم المستخدم أو كلمة المرور';
    } else if (error.code === 'ECONNREFUSED') {
      errorMessage = 'لا يمكن الوصول إلى الخادم. تحقق من اسم الخادم والمنفذ';
    } else if (error.code === 'ETIMEOUT') {
      errorMessage = 'انتهت مهلة الاتصال. تحقق من إعدادات الشبكة';
    } else if (error.code === 'ENOTFOUND') {
      errorMessage = 'لم يتم العثور على الخادم. تحقق من اسم الخادم';
    } else if (error.message) {
      errorMessage = error.message;
    }

    return NextResponse.json(
      {
        success: false,
        error: errorMessage,
        details: error.code || 'UNKNOWN_ERROR'
      },
      { status: 400 }
    );
  }
}
