'use client';

import { useState } from 'react';
import DatabaseSetup from '@/components/DatabaseSetup';
import MainDashboard from '@/components/MainDashboard';

export default function Home() {
  const [isConnected, setIsConnected] = useState(false);
  const [connectionData, setConnectionData] = useState(null);

  const handleConnectionSuccess = (data: any) => {
    setConnectionData(data);
    setIsConnected(true);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      {!isConnected ? (
        <DatabaseSetup onConnectionSuccess={handleConnectionSuccess} />
      ) : (
        <MainDashboard connectionData={connectionData} />
      )}
    </div>
  );
}
