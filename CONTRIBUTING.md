# دليل المساهمة

نرحب بمساهماتكم في تطوير وكيل الذكاء الاصطناعي لـ SQL! هذا الدليل سيساعدكم على فهم كيفية المساهمة بفعالية.

## كيفية المساهمة

### 1. الإبلاغ عن الأخطاء

إذا وجدت خطأ، يرجى:
- التحقق من أن الخطأ لم يتم الإبلاغ عنه مسبقاً في [Issues](../../issues)
- إنشاء issue جديد مع:
  - وصف واضح للمشكلة
  - خطوات إعادة إنتاج الخطأ
  - البيئة المستخدمة (نظام التشغيل، إصدار Node.js، إلخ)
  - لقطات شاشة إن أمكن

### 2. اقتراح ميزات جديدة

لاقتراح ميزة جديدة:
- تحقق من أن الميزة لم يتم اقتراحها مسبقاً
- أنشئ issue مع تسمية "enhancement"
- اشرح الميزة المقترحة وفائدتها
- قدم أمثلة على الاستخدام

### 3. المساهمة بالكود

#### إعداد البيئة التطويرية

```bash
# استنساخ المشروع
git clone <repository-url>
cd sql-ai-agent

# تثبيت التبعيات
npm install

# نسخ ملف البيئة
cp .env.local.example .env.local

# تحديث متغيرات البيئة في .env.local
# اختبار الاتصال بقاعدة البيانات
npm run test:db

# تشغيل التطبيق
npm run dev
```

#### معايير الكود

- استخدم TypeScript لجميع الملفات الجديدة
- اتبع معايير ESLint المحددة في المشروع
- اكتب تعليقات واضحة باللغة العربية
- استخدم أسماء متغيرات وصفية

#### هيكل المشروع

```
src/
├── app/                 # صفحات Next.js و API routes
├── components/          # مكونات React قابلة لإعادة الاستخدام
├── lib/                 # مكتبات ووظائف مساعدة
├── data/                # ملفات البيانات والتكوين
└── types/               # تعريفات TypeScript
```

#### إرشادات التطوير

1. **المكونات (Components)**:
   - كل مكون في ملف منفصل
   - استخدم TypeScript interfaces للProps
   - أضف تعليقات JSDoc للمكونات المعقدة

2. **API Routes**:
   - معالجة الأخطاء بشكل صحيح
   - التحقق من صحة البيانات المدخلة
   - إرجاع رسائل خطأ واضحة باللغة العربية

3. **الأنماط (Styling)**:
   - استخدم Tailwind CSS
   - اتبع نظام التصميم الموحد
   - تأكد من دعم RTL للغة العربية

#### عملية Pull Request

1. أنشئ فرع جديد من `main`:
   ```bash
   git checkout -b feature/اسم-الميزة
   ```

2. اعمل على التغييرات المطلوبة

3. تأكد من أن الكود يعمل:
   ```bash
   npm run lint
   npm run type-check
   npm run build
   ```

4. اكتب رسالة commit واضحة:
   ```bash
   git commit -m "إضافة: ميزة جديدة لتحليل البيانات"
   ```

5. ادفع التغييرات:
   ```bash
   git push origin feature/اسم-الميزة
   ```

6. أنشئ Pull Request مع:
   - عنوان واضح
   - وصف مفصل للتغييرات
   - ربط بـ Issues ذات الصلة
   - لقطات شاشة للتغييرات في الواجهة

### 4. مراجعة الكود

جميع Pull Requests تخضع لمراجعة الكود. نحن نبحث عن:
- جودة الكود وقابليته للقراءة
- اتباع معايير المشروع
- اختبار الوظائف الجديدة
- توثيق مناسب

### 5. الاختبار

- اختبر التغييرات على متصفحات مختلفة
- تأكد من عمل الميزات الموجودة
- اختبر مع بيانات مختلفة
- تحقق من الأداء

## معايير الجودة

### الكود
- استخدام TypeScript بشكل صحيح
- معالجة الأخطاء المناسبة
- كود قابل للقراءة والصيانة
- تجنب التكرار (DRY principle)

### الواجهة
- تصميم متجاوب
- دعم كامل للغة العربية (RTL)
- إمكانية الوصول (Accessibility)
- تجربة مستخدم سلسة

### الأداء
- تحميل سريع للصفحات
- استخدام فعال للذاكرة
- تحسين الاستعلامات
- تخزين مؤقت ذكي

## الحصول على المساعدة

إذا كنت بحاجة للمساعدة:
- راجع الوثائق في README.md
- ابحث في Issues الموجودة
- أنشئ issue جديد مع تسمية "question"
- تواصل مع المطورين

## رخصة المساهمة

بمساهمتك في هذا المشروع، فإنك توافق على أن مساهماتك ستكون مرخصة تحت نفس رخصة المشروع (MIT License).

## شكر وتقدير

نشكر جميع المساهمين الذين يساعدون في تطوير هذا المشروع وجعله أفضل للجميع.

---

**ملاحظة**: هذا المشروع يتبع [Contributor Covenant Code of Conduct](CODE_OF_CONDUCT.md). بالمشاركة، من المتوقع أن تلتزم بهذا الميثاق.
